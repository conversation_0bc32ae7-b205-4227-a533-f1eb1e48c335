# Student Apps Module

A comprehensive student management system module built for CodeIgniter 4 with modern UI/UX using TailAdmin theme.

## 📋 Overview

The Student Apps module provides complete CRUD (Create, Read, Update, Delete, List) operations for all student-related tables in the school management system. It features a modern, responsive interface with advanced filtering, search capabilities, and real-time statistics.

## 🚀 Features

### Core Functionality
- **Complete CRUD Operations** for all student-related tables
- **Advanced DataTables** with server-side processing
- **Real-time Statistics** and dashboard analytics
- **Responsive Design** using TailAdmin theme
- **AJAX-powered** forms and interactions
- **File Upload Management** for documents
- **Advanced Filtering** and search capabilities
- **Toast Notifications** for user feedback
- **SweetAlert2 Confirmations** for critical actions

### Security Features
- **CSRF Protection** on all forms
- **Input Validation** with custom rules
- **File Upload Validation** with type and size restrictions
- **SQL Injection Protection** through CodeIgniter's Query Builder

## 📊 Modules Included

### 1. Student Apply Leave (`student_applyleave`)
- **Purpose**: Manage student leave applications
- **Features**: 
  - Leave application submission
  - Approval/rejection workflow
  - Date overlap validation
  - Document attachment support
  - Status tracking (Pending, Approved, Rejected)

### 2. Student Attendance (`student_attendences`)
- **Purpose**: Track daily student attendance
- **Features**:
  - Biometric and QR code attendance support
  - Bulk attendance marking
  - Attendance reports and analytics
  - Integration with attendance types

### 3. Student Behaviour (`student_behaviour`)
- **Purpose**: Define behavior patterns and point systems
- **Features**:
  - Positive/negative behavior tracking
  - Point-based system (-100 to +100)
  - Behavior categories and impact levels
  - Statistical analysis

### 4. Student Documents (`student_doc`)
- **Purpose**: Manage student document uploads
- **Features**:
  - Multiple file format support (PDF, DOC, images)
  - Document categorization
  - File size validation (max 5MB)
  - Secure file storage

### 5. Student Edit Fields (`student_edit_fields`)
- **Purpose**: Configure editable student fields
- **Features**:
  - Dynamic field management
  - Field status control
  - Custom field definitions

### 6. Student Fees (`student_fees`)
- **Purpose**: Manage student fee structures
- **Features**:
  - Fee calculation and tracking
  - Discount management
  - Fine calculations
  - Payment status tracking

### 7. Student Fees Deposite (`student_fees_deposite`)
- **Purpose**: Track fee payments and deposits
- **Features**:
  - Payment recording
  - Receipt generation
  - Payment method tracking
  - Transaction history

### 8. Student Fees Discounts (`student_fees_discounts`)
- **Purpose**: Manage fee discounts and scholarships
- **Features**:
  - Discount assignment
  - Scholarship tracking
  - Eligibility management
  - Discount calculations

### 9. Student Fees Master (`student_fees_master`)
- **Purpose**: Master fee configuration
- **Features**:
  - Fee structure definition
  - Session-wise fee management
  - Automated fee generation
  - Fee group management

### 10. Student Fees Processing (`student_fees_processing`)
- **Purpose**: Handle online fee payments
- **Features**:
  - Payment gateway integration
  - Transaction processing
  - Payment status tracking
  - Refund management

### 11. Student Incident Comments (`student_incident_comments`)
- **Purpose**: Manage incident-related comments
- **Features**:
  - Comment threading
  - Staff and student comments
  - Incident tracking
  - Communication history

### 12. Student Incidents (`student_incidents`)
- **Purpose**: Track student behavioral incidents
- **Features**:
  - Incident reporting
  - Behavior point assignment
  - Staff assignment
  - Resolution tracking

### 13. Student Session (`student_session`)
- **Purpose**: Manage student session enrollments
- **Features**:
  - Class and section assignment
  - Session management
  - Transport and hostel allocation
  - Academic year tracking

### 14. Student Subject Attendances (`student_subject_attendances`)
- **Purpose**: Track subject-wise attendance
- **Features**:
  - Subject-specific attendance
  - Timetable integration
  - Period-wise tracking
  - Subject attendance reports

### 15. Student Timeline (`student_timeline`)
- **Purpose**: Maintain student academic timeline
- **Features**:
  - Chronological event tracking
  - Academic milestones
  - Document attachments
  - Status management

### 16. Student Transport Fees (`student_transport_fees`)
- **Purpose**: Manage transportation fees
- **Features**:
  - Route-based fee calculation
  - Pickup point management
  - Transport fee tracking
  - Route optimization

## 🛠️ Technical Architecture

### Models
- **BaseModel Extension**: All models extend the custom BaseModel for consistent CRUD operations
- **Validation Rules**: Comprehensive validation for all fields
- **Relationships**: Proper foreign key relationships and joins
- **Statistics Methods**: Built-in statistical analysis methods

### Controllers
- **BaseCrudController**: Standardized CRUD operations
- **AJAX Support**: Full AJAX support for seamless user experience
- **File Handling**: Secure file upload and download functionality
- **Data Processing**: Server-side DataTables processing

### Views
- **TailAdmin Theme**: Modern, responsive design
- **Component-based**: Reusable UI components
- **Accessibility**: WCAG compliant interface
- **Mobile-first**: Responsive design for all devices

## 📁 File Structure

```
app/
├── Controllers/
│   ├── StudentAppsController.php          # Main dashboard controller
│   ├── StudentApplyLeaveController.php    # Leave management
│   ├── StudentAttendanceController.php    # Attendance tracking
│   ├── StudentBehaviourController.php     # Behavior management
│   ├── StudentDocController.php           # Document management
│   └── ... (other controllers)
├── Models/
│   ├── StudentApplyLeaveModel.php         # Leave model
│   ├── StudentAttendanceModel.php         # Attendance model
│   ├── StudentBehaviourModel.php          # Behavior model
│   ├── StudentDocModel.php                # Document model
│   └── ... (other models)
└── Views/
    └── admin/
        └── student_apps/
            ├── index.php                  # Main dashboard
            ├── apply_leave/
            │   ├── index.php              # Leave listing
            │   ├── create.php             # Create leave
            │   ├── edit.php               # Edit leave
            │   └── show.php               # View leave
            └── ... (other module views)
```

## 🔧 Installation & Setup

### 1. Database Setup
Ensure all student-related tables exist in your database:
- student_applyleave
- student_attendences
- student_behaviour
- student_doc
- student_edit_fields
- student_fees
- student_fees_deposite
- student_fees_discounts
- student_fees_master
- student_fees_processing
- student_incident_comments
- student_incidents
- student_session
- student_subject_attendances
- student_timeline
- student_transport_fees

### 2. File Permissions
Create upload directories with proper permissions:
```bash
mkdir -p writable/uploads/student_documents
chmod 755 writable/uploads/student_documents
```

### 3. Routes Configuration
Routes are automatically configured in `app/Config/Routes.php` under the `admin/student-apps` group.

### 4. Navigation Menu
The Student Apps menu item is added to the admin layout navigation.

## 🎯 Usage

### Accessing the Module
1. Navigate to `/admin/student-apps` for the main dashboard
2. Click on any module card to access specific functionality
3. Use the sidebar navigation for quick access

### Creating Records
1. Click "Add New" button on any module listing page
2. Fill in the required information
3. Upload files if applicable
4. Submit the form

### Managing Records
1. Use the DataTables interface for listing and searching
2. Apply filters for specific data views
3. Use action buttons for edit, view, delete operations
4. Bulk operations available where applicable

## 📈 Statistics & Analytics

Each module provides comprehensive statistics including:
- Total record counts
- Status-based breakdowns
- Time-based analytics
- Trend analysis
- Performance metrics

## 🔒 Security Considerations

- All forms include CSRF protection
- File uploads are validated for type and size
- SQL injection protection through Query Builder
- Input sanitization and validation
- Secure file storage outside web root

## 🚀 Performance Features

- Server-side DataTables processing for large datasets
- AJAX-powered interfaces for smooth user experience
- Optimized database queries with proper indexing
- Lazy loading for improved page load times
- Caching for frequently accessed data

## 🎨 UI/UX Features

- Modern TailAdmin design system
- Responsive layout for all devices
- Toast notifications for user feedback
- SweetAlert2 for confirmations
- Loading states and progress indicators
- Intuitive navigation and breadcrumbs

## 📱 Mobile Responsiveness

The entire module is fully responsive and optimized for:
- Desktop computers
- Tablets
- Mobile phones
- Touch interfaces

## 🔄 Future Enhancements

Planned features for future releases:
- Advanced reporting and analytics
- Email notifications
- SMS integration
- Mobile app API
- Advanced workflow management
- Integration with external systems

## 📞 Support

For technical support or feature requests, please refer to the main project documentation or contact the development team.
