<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CheckSessionFiles extends BaseCommand
{
    protected $group       = 'Maintenance';
    protected $name        = 'check:session-files';
    protected $description = 'Check session files location and cleanup if needed';

    public function run(array $params)
    {
        CLI::write('Checking Session Files Location...', 'green');
        CLI::newLine();

        $this->checkSessionConfig();
        $this->checkCorrectLocation();
        $this->checkWrongLocations();
        $this->cleanupOldSessions();

        CLI::newLine();
        CLI::write('Session Files Check Complete!', 'green');
    }

    private function checkSessionConfig()
    {
        CLI::write('1. Checking Session Configuration...', 'yellow');
        
        $config = config('Session');
        
        CLI::write("   Driver: {$config->driver}", 'cyan');
        CLI::write("   Save Path: {$config->savePath}", 'cyan');
        CLI::write("   Cookie Name: {$config->cookieName}", 'cyan');
        CLI::write("   Expiration: {$config->expiration} seconds", 'cyan');
        
        if ($config->savePath === WRITEPATH . 'session') {
            CLI::write("   ✓ Session save path is correct", 'green');
        } else {
            CLI::write("   ✗ Session save path is incorrect", 'red');
        }
    }

    private function checkCorrectLocation()
    {
        CLI::write('2. Checking Correct Session Location...', 'yellow');
        
        $sessionPath = WRITEPATH . 'session';
        
        if (is_dir($sessionPath)) {
            CLI::write("   ✓ Session directory exists: {$sessionPath}", 'green');
            
            $files = glob($sessionPath . '/ci_session*');
            $count = count($files);
            
            CLI::write("   ✓ Found {$count} session files in correct location", 'green');
            
            // Check permissions
            if (is_writable($sessionPath)) {
                CLI::write("   ✓ Session directory is writable", 'green');
            } else {
                CLI::write("   ✗ Session directory is not writable", 'red');
            }
        } else {
            CLI::write("   ✗ Session directory does not exist: {$sessionPath}", 'red');
        }
    }

    private function checkWrongLocations()
    {
        CLI::write('3. Checking for Session Files in Wrong Locations...', 'yellow');
        
        $wrongLocations = [
            'public/null',
            'public/session',
            'public/ci_session',
            ROOTPATH . 'null',
            ROOTPATH . 'session'
        ];
        
        $foundWrongFiles = false;
        
        foreach ($wrongLocations as $location) {
            if (is_dir($location)) {
                $files = glob($location . '/ci_session*');
                if (!empty($files)) {
                    $count = count($files);
                    CLI::write("   ✗ Found {$count} session files in wrong location: {$location}", 'red');
                    $foundWrongFiles = true;
                    
                    // Ask if user wants to clean up
                    if (CLI::prompt("   Do you want to remove these files? (y/n)", ['y', 'n']) === 'y') {
                        $this->cleanupWrongLocation($location);
                    }
                } else {
                    CLI::write("   ✓ No session files found in: {$location}", 'green');
                }
            } else {
                CLI::write("   ✓ Directory does not exist: {$location}", 'green');
            }
        }
        
        if (!$foundWrongFiles) {
            CLI::write("   ✓ No session files found in wrong locations", 'green');
        }
    }

    private function cleanupOldSessions()
    {
        CLI::write('4. Checking for Old Session Files...', 'yellow');
        
        $sessionPath = WRITEPATH . 'session';
        $files = glob($sessionPath . '/ci_session*');
        $expiredCount = 0;
        $config = config('Session');
        $expiration = $config->expiration;
        
        foreach ($files as $file) {
            $fileTime = filemtime($file);
            $currentTime = time();
            
            if (($currentTime - $fileTime) > $expiration) {
                $expiredCount++;
            }
        }
        
        if ($expiredCount > 0) {
            CLI::write("   Found {$expiredCount} expired session files", 'yellow');
            
            if (CLI::prompt("   Do you want to remove expired session files? (y/n)", ['y', 'n']) === 'y') {
                $removed = 0;
                foreach ($files as $file) {
                    $fileTime = filemtime($file);
                    $currentTime = time();
                    
                    if (($currentTime - $fileTime) > $expiration) {
                        if (unlink($file)) {
                            $removed++;
                        }
                    }
                }
                CLI::write("   ✓ Removed {$removed} expired session files", 'green');
            }
        } else {
            CLI::write("   ✓ No expired session files found", 'green');
        }
    }

    private function cleanupWrongLocation($location)
    {
        try {
            $files = glob($location . '/ci_session*');
            $removed = 0;
            
            foreach ($files as $file) {
                if (unlink($file)) {
                    $removed++;
                }
            }
            
            // Try to remove the directory if it's empty
            if (is_dir($location) && count(scandir($location)) === 2) { // Only . and ..
                rmdir($location);
                CLI::write("   ✓ Removed empty directory: {$location}", 'green');
            }
            
            CLI::write("   ✓ Removed {$removed} session files from wrong location", 'green');
            
        } catch (\Exception $e) {
            CLI::write("   ✗ Error cleaning up {$location}: " . $e->getMessage(), 'red');
        }
    }
}
