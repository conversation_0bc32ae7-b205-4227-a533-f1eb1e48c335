<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class CreateSections extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'db:create-sections';
    protected $description = 'Create sections table and insert default data';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        // Check if sections table exists
        if ($db->tableExists('sections')) {
            CLI::write('Sections table already exists.', 'yellow');
            return;
        }

        CLI::write('Creating sections table...', 'green');

        $sql = "CREATE TABLE `sections` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `section` varchar(60) NOT NULL,
            `is_active` varchar(255) DEFAULT 'no',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` date DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `section` (`section`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        try {
            $db->query($sql);
            CLI::write('Sections table created successfully!', 'green');

            // Insert default sections
            $data = [
                ['section' => 'A', 'is_active' => 'yes'],
                ['section' => 'B', 'is_active' => 'yes'],
                ['section' => 'C', 'is_active' => 'yes'],
                ['section' => 'D', 'is_active' => 'yes'],
            ];

            $db->table('sections')->insertBatch($data);
            CLI::write('Default sections inserted successfully!', 'green');

        } catch (\Exception $e) {
            CLI::write('Error: ' . $e->getMessage(), 'red');
        }
    }
}
