<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\DashboardModel;

class DebugDashboard extends BaseCommand
{
    protected $group       = 'Admin';
    protected $name        = 'admin:debug';
    protected $description = 'Debug admin dashboard data and functionality';

    public function run(array $params)
    {
        CLI::write('====================================', 'green');
        CLI::write('Dashboard Data Debug', 'green');
        CLI::write('====================================', 'green');
        CLI::newLine();

        $db = \Config\Database::connect();

        // Check Students
        CLI::write('1. Students Data:', 'yellow');
        try {
            $students = $db->table('students')->get()->getResultArray();
            CLI::write('   Total Students: ' . count($students), 'white');
            if (!empty($students)) {
                foreach ($students as $student) {
                    CLI::write("   - {$student['firstname']} {$student['lastname']} (ID: {$student['id']})", 'light_gray');
                }
            }
        } catch (\Exception $e) {
            CLI::write('   Error: ' . $e->getMessage(), 'red');
        }
        CLI::newLine();

        // Check Staff
        CLI::write('2. Staff Data:', 'yellow');
        try {
            $staff = $db->table('staff')->get()->getResultArray();
            CLI::write('   Total Staff: ' . count($staff), 'white');
            if (!empty($staff)) {
                foreach ($staff as $member) {
                    CLI::write("   - {$member['name']} ({$member['designation']})", 'light_gray');
                }
            }
        } catch (\Exception $e) {
            CLI::write('   Error: ' . $e->getMessage(), 'red');
        }
        CLI::newLine();

        // Check Classes
        CLI::write('3. Classes Data:', 'yellow');
        try {
            $classes = $db->table('classes')->get()->getResultArray();
            CLI::write('   Total Classes: ' . count($classes), 'white');
            if (!empty($classes)) {
                foreach ($classes as $class) {
                    CLI::write("   - {$class['class']} (ID: {$class['id']})", 'light_gray');
                }
            }
        } catch (\Exception $e) {
            CLI::write('   Error: ' . $e->getMessage(), 'red');
        }
        CLI::newLine();

        // Check Expenses
        CLI::write('4. Expenses Data:', 'yellow');
        try {
            $expenses = $db->table('expenses')->get()->getResultArray();
            CLI::write('   Total Expenses: ' . count($expenses), 'white');
            $totalAmount = 0;
            if (!empty($expenses)) {
                foreach ($expenses as $expense) {
                    CLI::write("   - {$expense['name']}: $" . number_format($expense['amount'], 2), 'light_gray');
                    $totalAmount += $expense['amount'];
                }
                CLI::write('   Total Amount: $' . number_format($totalAmount, 2), 'cyan');
            }
        } catch (\Exception $e) {
            CLI::write('   Error: ' . $e->getMessage(), 'red');
        }
        CLI::newLine();

        // Test Dashboard Model
        CLI::write('5. Dashboard Model Test:', 'yellow');
        try {
            $dashboardModel = new DashboardModel();
            
            $stats = $dashboardModel->getDashboardStats();
            CLI::write('   Dashboard Stats:', 'white');
            CLI::write("   - Total Students: {$stats['total_students']}", 'light_gray');
            CLI::write("   - Total Staff: {$stats['total_staff']}", 'light_gray');
            CLI::write("   - Total Classes: {$stats['total_classes']}", 'light_gray');
            CLI::write('   - Monthly Expenses: $' . number_format($stats['monthly_expenses'], 2), 'light_gray');
            
            $activities = $dashboardModel->getRecentActivities();
            CLI::write('   Recent Activities: ' . count($activities) . ' items', 'light_gray');
            
            $charts = $dashboardModel->getChartsData();
            CLI::write('   Charts Data:', 'white');
            CLI::write('   - Enrollments: ' . count($charts['enrollments']) . ' data points', 'light_gray');
            CLI::write('   - Expenses: ' . count($charts['expenses']) . ' categories', 'light_gray');
            
        } catch (\Exception $e) {
            CLI::write('   Error: ' . $e->getMessage(), 'red');
        }
        CLI::newLine();

        // Check table structure
        CLI::write('6. Table Structure Check:', 'yellow');
        $tables = ['students', 'staff', 'classes', 'expenses', 'expense_head'];
        foreach ($tables as $table) {
            try {
                if ($db->tableExists($table)) {
                    $fields = $db->getFieldNames($table);
                    CLI::write("   ✓ $table exists with " . count($fields) . ' columns', 'green');
                } else {
                    CLI::write("   ✗ $table does not exist", 'red');
                }
            } catch (\Exception $e) {
                CLI::write("   ⚠ $table check failed: " . $e->getMessage(), 'yellow');
            }
        }
        CLI::newLine();

        CLI::write('====================================', 'green');
        CLI::write('Debug Complete!', 'green');
        CLI::write('====================================', 'green');
        CLI::write('If you see data above, the dashboard should work properly.', 'white');
        CLI::write('Access your dashboard at: http://studentwablas.me/admin', 'cyan');
        CLI::newLine();
    }
}
