<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class MakeStudentViews extends BaseCommand
{
    protected $group       = 'Generators';
    protected $name        = 'make:student-views';
    protected $description = 'Generate missing Student Apps CRUD views';

    private $modules = [
        'apply_leave' => [
            'title' => 'Leave Application',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'from_date' => ['type' => 'date', 'label' => 'From Date', 'required' => true],
                'to_date' => ['type' => 'date', 'label' => 'To Date', 'required' => true],
                'reason' => ['type' => 'textarea', 'label' => 'Reason', 'required' => true],
                'docs' => ['type' => 'file', 'label' => 'Supporting Documents', 'required' => false]
            ]
        ],
        'attendance' => [
            'title' => 'Student Attendance',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'date' => ['type' => 'date', 'label' => 'Date', 'required' => true],
                'attendence_type_id' => ['type' => 'select', 'label' => 'Attendance Type', 'required' => true],
                'remark' => ['type' => 'textarea', 'label' => 'Remark', 'required' => false]
            ]
        ],
        'behaviour' => [
            'title' => 'Student Behaviour',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'incident_date' => ['type' => 'date', 'label' => 'Incident Date', 'required' => true],
                'title' => ['type' => 'text', 'label' => 'Title', 'required' => true],
                'description' => ['type' => 'textarea', 'label' => 'Description', 'required' => true],
                'action_taken' => ['type' => 'textarea', 'label' => 'Action Taken', 'required' => false]
            ]
        ],
        'documents' => [
            'title' => 'Student Document',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'title' => ['type' => 'text', 'label' => 'Document Title', 'required' => true],
                'doc_type' => ['type' => 'select', 'label' => 'Document Type', 'required' => true],
                'doc_file' => ['type' => 'file', 'label' => 'Document File', 'required' => true],
                'description' => ['type' => 'textarea', 'label' => 'Description', 'required' => false]
            ]
        ],
        'fees' => [
            'title' => 'Student Fees',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'fee_type' => ['type' => 'select', 'label' => 'Fee Type', 'required' => true],
                'amount' => ['type' => 'number', 'label' => 'Amount', 'required' => true],
                'due_date' => ['type' => 'date', 'label' => 'Due Date', 'required' => true],
                'status' => ['type' => 'select', 'label' => 'Status', 'required' => true]
            ]
        ],
        'incidents' => [
            'title' => 'Student Incident',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'incident_date' => ['type' => 'date', 'label' => 'Incident Date', 'required' => true],
                'title' => ['type' => 'text', 'label' => 'Title', 'required' => true],
                'description' => ['type' => 'textarea', 'label' => 'Description', 'required' => true],
                'action_taken' => ['type' => 'textarea', 'label' => 'Action Taken', 'required' => false]
            ]
        ],
        'sessions' => [
            'title' => 'Student Session',
            'fields' => [
                'student_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'session_id' => ['type' => 'select', 'label' => 'Session', 'required' => true],
                'class_id' => ['type' => 'select', 'label' => 'Class', 'required' => true],
                'section_id' => ['type' => 'select', 'label' => 'Section', 'required' => true],
                'is_active' => ['type' => 'select', 'label' => 'Status', 'required' => true]
            ]
        ],
        'subject_attendance' => [
            'title' => 'Subject Attendance',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'subject_id' => ['type' => 'select', 'label' => 'Subject', 'required' => true],
                'date' => ['type' => 'date', 'label' => 'Date', 'required' => true],
                'attendence_type' => ['type' => 'select', 'label' => 'Attendance Type', 'required' => true],
                'remark' => ['type' => 'textarea', 'label' => 'Remark', 'required' => false]
            ]
        ],
        'timeline' => [
            'title' => 'Student Timeline',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'title' => ['type' => 'text', 'label' => 'Title', 'required' => true],
                'description' => ['type' => 'textarea', 'label' => 'Description', 'required' => true],
                'date' => ['type' => 'date', 'label' => 'Date', 'required' => true],
                'status' => ['type' => 'select', 'label' => 'Status', 'required' => true]
            ]
        ],
        'transport_fees' => [
            'title' => 'Transport Fees',
            'fields' => [
                'student_session_id' => ['type' => 'select', 'label' => 'Student', 'required' => true],
                'route_id' => ['type' => 'select', 'label' => 'Route', 'required' => true],
                'amount' => ['type' => 'number', 'label' => 'Amount', 'required' => true],
                'month' => ['type' => 'select', 'label' => 'Month', 'required' => true],
                'year' => ['type' => 'number', 'label' => 'Year', 'required' => true]
            ]
        ]
    ];

    public function run(array $params)
    {
        CLI::write('Generating missing Student Apps views...', 'green');
        CLI::newLine();

        foreach ($this->modules as $module => $config) {
            $this->generateModuleViews($module, $config);
        }

        CLI::newLine();
        CLI::write('All Student Apps views generated successfully!', 'green');
    }

    private function generateModuleViews($module, $config)
    {
        CLI::write("Generating views for {$module}...", 'yellow');

        $viewDir = APPPATH . "Views/admin/student_apps/{$module}";

        // Create directory if it doesn't exist
        if (!is_dir($viewDir)) {
            mkdir($viewDir, 0755, true);
            CLI::write("  Created directory: {$viewDir}", 'green');
        }

        // Generate missing views
        $views = ['index', 'create', 'edit', 'show'];

        foreach ($views as $view) {
            $viewFile = $viewDir . "/{$view}.php";

            if (!file_exists($viewFile)) {
                $content = $this->generateViewContent($view, $module, $config);
                file_put_contents($viewFile, $content);
                CLI::write("  ✓ Generated {$view}.php", 'green');
            } else {
                CLI::write("  - {$view}.php already exists", 'yellow');
            }
        }
    }

    private function generateViewContent($view, $module, $config)
    {
        $title = $config['title'];
        $routePrefix = 'admin/student-apps/' . str_replace('_', '-', $module);

        switch ($view) {
            case 'index':
                return $this->generateIndexView($title, $routePrefix);
            case 'create':
                return $this->generateCreateView($title, $routePrefix, $config['fields']);
            case 'edit':
                return $this->generateEditView($title, $routePrefix, $config['fields']);
            case 'show':
                return $this->generateShowView($title, $routePrefix, $config['fields']);
            default:
                return '';
        }
    }

    private function generateIndexView($title, $routePrefix)
    {
        return "<?= \$this->extend('admin/layouts/main') ?>

<?= \$this->section('title') ?>{$title} Management<?= \$this->endSection() ?>

<?= \$this->section('content') ?>
<div class=\"mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10\">
    <!-- Breadcrumb -->
    <div class=\"mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between\">
        <h2 class=\"text-title-md2 font-semibold text-black dark:text-white\">
            {$title} Management
        </h2>
        <nav>
            <ol class=\"flex items-center gap-2\">
                <?php foreach (\$breadcrumbs as \$index => \$breadcrumb): ?>
                    <?php if (\$index === count(\$breadcrumbs) - 1): ?>
                        <li class=\"text-primary\"><?= \$breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class=\"font-medium\" href=\"<?= \$breadcrumb['url'] ?>\"><?= \$breadcrumb['name'] ?></a></li>
                        <li class=\"text-body-color\">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Action Buttons -->
    <div class=\"mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between\">
        <div class=\"flex gap-3\">
            <a href=\"<?= base_url('{$routePrefix}/create') ?>\" class=\"inline-flex items-center justify-center rounded-md bg-primary py-2 px-4 text-center font-medium text-white hover:bg-opacity-90\">
                <i class=\"fas fa-plus mr-2\"></i>
                Add New {$title}
            </a>
        </div>
    </div>

    <!-- Data Table -->
    <div class=\"rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark\">
        <div class=\"py-6 px-4 md:px-6 xl:px-7.5\">
            <h4 class=\"text-xl font-semibold text-black dark:text-white\">
                {$title} List
            </h4>
        </div>

        <div class=\"p-4 md:p-6 xl:p-7.5\">
            <div class=\"overflow-x-auto\">
                <table id=\"dataTable\" class=\"w-full table-auto\">
                    <thead>
                        <tr class=\"bg-gray-2 text-left dark:bg-meta-4\">
                            <th class=\"py-4 px-4 font-medium text-black dark:text-white\">
                                <input type=\"checkbox\" id=\"selectAll\" class=\"rounded\">
                            </th>
                            <th class=\"py-4 px-4 font-medium text-black dark:text-white\">ID</th>
                            <th class=\"py-4 px-4 font-medium text-black dark:text-white\">Details</th>
                            <th class=\"py-4 px-4 font-medium text-black dark:text-white\">Status</th>
                            <th class=\"py-4 px-4 font-medium text-black dark:text-white\">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
\$(document).ready(function() {
    // Initialize DataTable
    const table = \$('#dataTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url(\"{$routePrefix}/get-data\") ?>',
            type: 'POST'
        },
        columns: [
            { data: 'checkbox', orderable: false, searchable: false },
            { data: 'id' },
            { data: 'details' },
            { data: 'status' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true
    });
});
</script>
<?= \$this->endSection() ?>";
    }

    private function generateCreateView($title, $routePrefix, $fields)
    {
        $formFields = $this->generateFormFields($fields);

        return "<?= \$this->extend('admin/layouts/main') ?>

<?= \$this->section('title') ?>Create {$title}<?= \$this->endSection() ?>

<?= \$this->section('content') ?>
<div class=\"mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10\">
    <!-- Breadcrumb -->
    <div class=\"mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between\">
        <h2 class=\"text-title-md2 font-semibold text-black dark:text-white\">
            Create {$title}
        </h2>
        <nav>
            <ol class=\"flex items-center gap-2\">
                <?php foreach (\$breadcrumbs as \$index => \$breadcrumb): ?>
                    <?php if (\$index === count(\$breadcrumbs) - 1): ?>
                        <li class=\"text-primary\"><?= \$breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class=\"font-medium\" href=\"<?= \$breadcrumb['url'] ?>\"><?= \$breadcrumb['name'] ?></a></li>
                        <li class=\"text-body-color\">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Form Card -->
    <div class=\"rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark\">
        <div class=\"border-b border-stroke py-4 px-6.5 dark:border-strokedark\">
            <h3 class=\"font-medium text-black dark:text-white\">
                Create New {$title}
            </h3>
        </div>

        <form id=\"createForm\" action=\"<?= base_url('{$routePrefix}/store') ?>\" method=\"POST\" enctype=\"multipart/form-data\">
            <?= csrf_field() ?>
            <div class=\"p-6.5\">
{$formFields}
                <!-- Action Buttons -->
                <div class=\"flex gap-4.5\">
                    <button type=\"submit\" class=\"flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90\">
                        <i class=\"fas fa-save mr-2\"></i>
                        Create {$title}
                    </button>
                    <a href=\"<?= base_url('{$routePrefix}') ?>\" class=\"flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white\">
                        <i class=\"fas fa-times mr-2\"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Show loading
        Swal.fire({
            title: 'Creating {$title}...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: data.message || '{$title} created successfully'
                }).then(() => {
                    window.location.href = '{$routePrefix}';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: data.message || 'Failed to create {$title}'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'An unexpected error occurred'
            });
        });
    });
});
</script>
<?= \$this->endSection() ?>";
    }

    private function generateEditView($title, $routePrefix, $fields)
    {
        $formFields = $this->generateFormFields($fields, true);

        return "<?= \$this->extend('admin/layouts/main') ?>

<?= \$this->section('title') ?>Edit {$title}<?= \$this->endSection() ?>

<?= \$this->section('content') ?>
<div class=\"mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10\">
    <!-- Breadcrumb -->
    <div class=\"mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between\">
        <h2 class=\"text-title-md2 font-semibold text-black dark:text-white\">
            Edit {$title}
        </h2>
        <nav>
            <ol class=\"flex items-center gap-2\">
                <?php foreach (\$breadcrumbs as \$index => \$breadcrumb): ?>
                    <?php if (\$index === count(\$breadcrumbs) - 1): ?>
                        <li class=\"text-primary\"><?= \$breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class=\"font-medium\" href=\"<?= \$breadcrumb['url'] ?>\"><?= \$breadcrumb['name'] ?></a></li>
                        <li class=\"text-body-color\">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Form Card -->
    <div class=\"rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark\">
        <div class=\"border-b border-stroke py-4 px-6.5 dark:border-strokedark\">
            <h3 class=\"font-medium text-black dark:text-white\">
                Edit {$title}
            </h3>
        </div>

        <form id=\"editForm\" action=\"<?= base_url('{$routePrefix}/update/' . \$record['id']) ?>\" method=\"POST\" enctype=\"multipart/form-data\">
            <?= csrf_field() ?>
            <div class=\"p-6.5\">
{$formFields}
                <!-- Action Buttons -->
                <div class=\"flex gap-4.5\">
                    <button type=\"submit\" class=\"flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90\">
                        <i class=\"fas fa-save mr-2\"></i>
                        Update {$title}
                    </button>
                    <a href=\"<?= base_url('{$routePrefix}') ?>\" class=\"flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white\">
                        <i class=\"fas fa-times mr-2\"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Show loading
        Swal.fire({
            title: 'Updating {$title}...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: data.message || '{$title} updated successfully'
                }).then(() => {
                    window.location.href = '{$routePrefix}';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: data.message || 'Failed to update {$title}'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'An unexpected error occurred'
            });
        });
    });
});
</script>
<?= \$this->endSection() ?>";
    }

    private function generateShowView($title, $routePrefix, $fields)
    {
        return "<?= \$this->extend('admin/layouts/main') ?>

<?= \$this->section('title') ?>View {$title}<?= \$this->endSection() ?>

<?= \$this->section('content') ?>
<div class=\"mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10\">
    <!-- Breadcrumb -->
    <div class=\"mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between\">
        <h2 class=\"text-title-md2 font-semibold text-black dark:text-white\">
            View {$title}
        </h2>
        <nav>
            <ol class=\"flex items-center gap-2\">
                <?php foreach (\$breadcrumbs as \$index => \$breadcrumb): ?>
                    <?php if (\$index === count(\$breadcrumbs) - 1): ?>
                        <li class=\"text-primary\"><?= \$breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class=\"font-medium\" href=\"<?= \$breadcrumb['url'] ?>\"><?= \$breadcrumb['name'] ?></a></li>
                        <li class=\"text-body-color\">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Details Card -->
    <div class=\"rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark\">
        <div class=\"border-b border-stroke py-4 px-6.5 dark:border-strokedark\">
            <h3 class=\"font-medium text-black dark:text-white\">
                {$title} Details
            </h3>
        </div>

        <div class=\"p-6.5\">
            <div class=\"grid grid-cols-1 gap-6 md:grid-cols-2\">
                <!-- Details will be displayed here -->
                <?php foreach (\$record as \$key => \$value): ?>
                    <div class=\"mb-4\">
                        <label class=\"mb-2.5 block text-black dark:text-white font-medium\">
                            <?= ucwords(str_replace('_', ' ', \$key)) ?>
                        </label>
                        <div class=\"w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition dark:border-form-strokedark dark:bg-form-input\">
                            <?= \$value ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Action Buttons -->
            <div class=\"mt-6 flex gap-4.5\">
                <a href=\"<?= base_url('{$routePrefix}/edit/' . \$record['id']) ?>\" class=\"flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90\">
                    <i class=\"fas fa-edit mr-2\"></i>
                    Edit {$title}
                </a>
                <a href=\"<?= base_url('{$routePrefix}') ?>\" class=\"flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white\">
                    <i class=\"fas fa-arrow-left mr-2\"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
<?= \$this->endSection() ?>";
    }

    private function generateFormFields($fields, $isEdit = false)
    {
        $html = '';
        $count = 0;

        foreach ($fields as $name => $config) {
            if ($count % 2 === 0) {
                $html .= "                <div class=\"mb-4.5 flex flex-col gap-6 xl:flex-row\">\n";
            }

            $required = $config['required'] ? 'required' : '';
            $requiredMark = $config['required'] ? '<span class="text-meta-1">*</span>' : '';
            $value = $isEdit ? "<?= \$record['{$name}'] ?? '' ?>" : '';

            $html .= "                    <!-- {$config['label']} -->\n";
            $html .= "                    <div class=\"w-full xl:w-1/2\">\n";
            $html .= "                        <label class=\"mb-2.5 block text-black dark:text-white\">\n";
            $html .= "                            {$config['label']} {$requiredMark}\n";
            $html .= "                        </label>\n";

            switch ($config['type']) {
                case 'text':
                case 'number':
                case 'date':
                    $html .= "                        <input type=\"{$config['type']}\" name=\"{$name}\" id=\"{$name}\" value=\"{$value}\" placeholder=\"Enter {$config['label']}\" \n";
                    $html .= "                               class=\"w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary\" {$required}>\n";
                    break;
                case 'textarea':
                    $html .= "                        <textarea name=\"{$name}\" id=\"{$name}\" rows=\"4\" placeholder=\"Enter {$config['label']}\" \n";
                    $html .= "                                  class=\"w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary\" {$required}>{$value}</textarea>\n";
                    break;
                case 'select':
                    $html .= "                        <select name=\"{$name}\" id=\"{$name}\" class=\"w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary\" {$required}>\n";
                    $html .= "                            <option value=\"\">Select {$config['label']}</option>\n";
                    $html .= "                            <!-- Options will be populated by controller -->\n";
                    $html .= "                        </select>\n";
                    break;
                case 'file':
                    $html .= "                        <input type=\"file\" name=\"{$name}\" id=\"{$name}\" \n";
                    $html .= "                               class=\"w-full cursor-pointer rounded-lg border-[1.5px] border-stroke bg-transparent font-medium outline-none transition file:mr-5 file:border-collapse file:cursor-pointer file:border-0 file:border-r file:border-solid file:border-stroke file:bg-whiter file:py-3 file:px-5 file:hover:bg-primary file:hover:bg-opacity-10 focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:file:border-form-strokedark dark:file:bg-white/30 dark:file:text-white dark:focus:border-primary\" {$required}>\n";
                    break;
            }

            $html .= "                    </div>\n";

            $count++;
            if ($count % 2 === 0 || $count === count($fields)) {
                $html .= "                </div>\n\n";
            }
        }

        return $html;
    }
}