<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestStudentApps extends BaseCommand
{
    protected $group       = 'Testing';
    protected $name        = 'test:student-apps';
    protected $description = 'Test Student Apps module functionality';

    public function run(array $params)
    {
        CLI::write('Testing Student Apps Module...', 'green');
        CLI::newLine();

        $this->testDatabase();
        $this->testControllers();
        $this->testModels();
        $this->testViews();
        $this->testRoutes();

        CLI::newLine();
        CLI::write('Student Apps Module Test Complete!', 'green');
    }

    private function testDatabase()
    {
        CLI::write('1. Testing Database Tables...', 'yellow');
        
        $db = \Config\Database::connect();
        
        $requiredTables = [
            'sections',
            'classes', 
            'students',
            'student_session',
            'student_applyleave',
            'student_attendences',
            'student_behaviour',
            'student_doc',
            'student_subject_attendances',
            'student_timeline',
            'student_transport_fees'
        ];

        foreach ($requiredTables as $table) {
            if ($db->tableExists($table)) {
                CLI::write("   ✓ Table '{$table}' exists", 'green');
            } else {
                CLI::write("   ✗ Table '{$table}' missing", 'red');
            }
        }

        // Test sections table data
        if ($db->tableExists('sections')) {
            $count = $db->table('sections')->countAllResults();
            CLI::write("   ✓ Sections table has {$count} records", 'green');
        }
    }

    private function testControllers()
    {
        CLI::write('2. Testing Controllers...', 'yellow');
        
        $requiredControllers = [
            'StudentAppsController',
            'StudentApplyLeaveController',
            'StudentAttendanceController',
            'StudentBehaviourController',
            'StudentDocController',
            'StudentSubjectAttendanceController',
            'StudentSessionController',
            'StudentTimelineController',
            'StudentTransportFeesController'
        ];

        foreach ($requiredControllers as $controller) {
            $controllerPath = APPPATH . 'Controllers/' . $controller . '.php';
            if (file_exists($controllerPath)) {
                CLI::write("   ✓ Controller '{$controller}' exists", 'green');
            } else {
                CLI::write("   ✗ Controller '{$controller}' missing", 'red');
            }
        }
    }

    private function testModels()
    {
        CLI::write('3. Testing Models...', 'yellow');
        
        $requiredModels = [
            'StudentsModel',
            'StudentSessionModel',
            'StudentApplyLeaveModel',
            'StudentAttendanceModel',
            'StudentBehaviourModel',
            'StudentDocModel',
            'StudentSubjectAttendanceModel',
            'SectionsModel',
            'ClassesModel',
            'SubjectsModel'
        ];

        foreach ($requiredModels as $model) {
            $modelPath = APPPATH . 'Models/' . $model . '.php';
            if (file_exists($modelPath)) {
                CLI::write("   ✓ Model '{$model}' exists", 'green');
                
                // Test model instantiation
                try {
                    $className = "\\App\\Models\\{$model}";
                    $instance = new $className();
                    CLI::write("   ✓ Model '{$model}' can be instantiated", 'green');
                } catch (\Exception $e) {
                    CLI::write("   ✗ Model '{$model}' instantiation failed: " . $e->getMessage(), 'red');
                }
            } else {
                CLI::write("   ✗ Model '{$model}' missing", 'red');
            }
        }
    }

    private function testViews()
    {
        CLI::write('4. Testing Views...', 'yellow');
        
        $requiredViews = [
            'admin/student_apps/index.php',
            'admin/student_apps/apply_leave/index.php',
            'admin/student_apps/apply_leave/create.php',
            'admin/student_apps/attendance/index.php',
            'admin/student_apps/behaviour/index.php',
            'admin/student_apps/documents/index.php',
            'admin/student_apps/documents/create.php',
            'admin/student_apps/subject_attendance/index.php',
            'admin/student_apps/sessions/index.php',
            'admin/student_apps/timeline/index.php',
            'admin/student_apps/transport_fees/index.php'
        ];

        foreach ($requiredViews as $view) {
            $viewPath = APPPATH . 'Views/' . $view;
            if (file_exists($viewPath)) {
                CLI::write("   ✓ View '{$view}' exists", 'green');
            } else {
                CLI::write("   ✗ View '{$view}' missing", 'red');
            }
        }
    }

    private function testRoutes()
    {
        CLI::write('5. Testing Routes...', 'yellow');
        
        $routes = service('routes');
        
        $testRoutes = [
            'admin/student-apps',
            'admin/student-apps/apply-leave',
            'admin/student-apps/attendance', 
            'admin/student-apps/behaviour',
            'admin/student-apps/documents',
            'admin/student-apps/documents/create',
            'admin/student-apps/subject-attendance',
            'admin/student-apps/sessions',
            'admin/student-apps/timeline',
            'admin/student-apps/transport-fees'
        ];

        foreach ($testRoutes as $route) {
            try {
                $routeInfo = $routes->getRoutes();
                CLI::write("   ✓ Route '{$route}' configured", 'green');
            } catch (\Exception $e) {
                CLI::write("   ✗ Route '{$route}' issue: " . $e->getMessage(), 'red');
            }
        }
    }
}
