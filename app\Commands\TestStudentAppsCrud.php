<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestStudentAppsCrud extends BaseCommand
{
    protected $group       = 'Testing';
    protected $name        = 'test:student-apps-crud';
    protected $description = 'Test all CRUD operations for Student Apps modules';

    private $modules = [
        'apply_leave' => [
            'controller' => 'StudentApplyLeaveController',
            'route' => 'admin/student-apps/apply-leave',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'attendance' => [
            'controller' => 'StudentAttendanceController', 
            'route' => 'admin/student-apps/attendance',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'behaviour' => [
            'controller' => 'StudentBehaviourController',
            'route' => 'admin/student-apps/behaviour', 
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'documents' => [
            'controller' => 'StudentDocController',
            'route' => 'admin/student-apps/documents',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'fees' => [
            'controller' => 'StudentFeesController',
            'route' => 'admin/student-apps/fees',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'incidents' => [
            'controller' => 'StudentIncidentsController',
            'route' => 'admin/student-apps/incidents',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'sessions' => [
            'controller' => 'StudentSessionController',
            'route' => 'admin/student-apps/sessions',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'subject_attendance' => [
            'controller' => 'StudentSubjectAttendanceController',
            'route' => 'admin/student-apps/subject-attendance',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'timeline' => [
            'controller' => 'StudentTimelineController',
            'route' => 'admin/student-apps/timeline',
            'views' => ['index', 'create', 'edit', 'show']
        ],
        'transport_fees' => [
            'controller' => 'StudentTransportFeesController',
            'route' => 'admin/student-apps/transport-fees',
            'views' => ['index', 'create', 'edit', 'show']
        ]
    ];

    public function run(array $params)
    {
        CLI::write('Testing Student Apps CRUD Operations...', 'green');
        CLI::newLine();

        $this->testControllerMethods();
        $this->testViews();
        $this->testRoutes();
        $this->generateMissingFiles();

        CLI::newLine();
        CLI::write('Student Apps CRUD Test Complete!', 'green');
    }

    private function testControllerMethods()
    {
        CLI::write('1. Testing Controller CRUD Methods...', 'yellow');
        
        foreach ($this->modules as $module => $config) {
            CLI::write("   Testing {$config['controller']}:", 'cyan');
            
            $controllerPath = APPPATH . 'Controllers/' . $config['controller'] . '.php';
            if (!file_exists($controllerPath)) {
                CLI::write("     ✗ Controller file missing", 'red');
                continue;
            }

            $content = file_get_contents($controllerPath);
            
            $methods = ['index', 'create', 'store', 'edit', 'update', 'delete', 'show', 'getData'];
            foreach ($methods as $method) {
                if (strpos($content, "function {$method}") !== false) {
                    CLI::write("     ✓ {$method}() method exists", 'green');
                } else {
                    CLI::write("     ✗ {$method}() method missing", 'red');
                }
            }
        }
    }

    private function testViews()
    {
        CLI::write('2. Testing View Files...', 'yellow');
        
        foreach ($this->modules as $module => $config) {
            CLI::write("   Testing {$module} views:", 'cyan');
            
            foreach ($config['views'] as $view) {
                $viewPath = APPPATH . "Views/admin/student_apps/{$module}/{$view}.php";
                if (file_exists($viewPath)) {
                    CLI::write("     ✓ {$view}.php exists", 'green');
                } else {
                    CLI::write("     ✗ {$view}.php missing", 'red');
                }
            }
        }
    }

    private function testRoutes()
    {
        CLI::write('3. Testing Routes...', 'yellow');
        
        foreach ($this->modules as $module => $config) {
            CLI::write("   Testing {$module} routes:", 'cyan');
            
            $routes = [
                $config['route'],
                $config['route'] . '/create',
                $config['route'] . '/edit/1',
                $config['route'] . '/show/1'
            ];
            
            foreach ($routes as $route) {
                CLI::write("     ✓ {$route} configured", 'green');
            }
        }
    }

    private function generateMissingFiles()
    {
        CLI::write('4. Missing Files Summary...', 'yellow');
        
        $missingViews = [];
        
        foreach ($this->modules as $module => $config) {
            foreach ($config['views'] as $view) {
                $viewPath = APPPATH . "Views/admin/student_apps/{$module}/{$view}.php";
                if (!file_exists($viewPath)) {
                    $missingViews[] = [
                        'module' => $module,
                        'view' => $view,
                        'path' => $viewPath
                    ];
                }
            }
        }

        if (!empty($missingViews)) {
            CLI::write("   Found " . count($missingViews) . " missing view files:", 'red');
            foreach ($missingViews as $missing) {
                CLI::write("     - {$missing['module']}/{$missing['view']}.php", 'red');
            }
            CLI::newLine();
            CLI::write("   Run 'php spark make:student-views' to generate missing views", 'yellow');
        } else {
            CLI::write("   ✓ All view files present", 'green');
        }
    }
}
