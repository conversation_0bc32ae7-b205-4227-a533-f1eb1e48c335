<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestStudentAppsFunctional extends BaseCommand
{
    protected $group       = 'Testing';
    protected $name        = 'test:student-apps-functional';
    protected $description = 'Functional test of Student Apps CRUD operations';

    private $baseUrl = 'http://studentwablas.me';
    private $modules = [
        'apply-leave' => 'Leave Applications',
        'attendance' => 'Student Attendance', 
        'behaviour' => 'Student Behaviour',
        'documents' => 'Student Documents',
        'fees' => 'Student Fees',
        'incidents' => 'Student Incidents',
        'sessions' => 'Student Sessions',
        'subject-attendance' => 'Subject Attendance',
        'timeline' => 'Student Timeline',
        'transport-fees' => 'Transport Fees'
    ];

    public function run(array $params)
    {
        CLI::write('Testing Student Apps Functional CRUD Operations...', 'green');
        CLI::newLine();

        $this->testMainDashboard();
        $this->testModulePages();
        $this->testCrudPages();

        CLI::newLine();
        CLI::write('Student Apps Functional Test Complete!', 'green');
    }

    private function testMainDashboard()
    {
        CLI::write('1. Testing Main Dashboard...', 'yellow');
        
        $url = $this->baseUrl . '/admin/student-apps';
        $response = $this->makeRequest($url);
        
        if ($response !== false) {
            CLI::write("   ✓ Main dashboard accessible: {$url}", 'green');
        } else {
            CLI::write("   ✗ Main dashboard failed: {$url}", 'red');
        }
    }

    private function testModulePages()
    {
        CLI::write('2. Testing Module Index Pages...', 'yellow');
        
        foreach ($this->modules as $module => $title) {
            $url = $this->baseUrl . "/admin/student-apps/{$module}";
            $response = $this->makeRequest($url);
            
            if ($response !== false) {
                CLI::write("   ✓ {$title} index page accessible", 'green');
            } else {
                CLI::write("   ✗ {$title} index page failed", 'red');
            }
        }
    }

    private function testCrudPages()
    {
        CLI::write('3. Testing CRUD Pages...', 'yellow');
        
        foreach ($this->modules as $module => $title) {
            CLI::write("   Testing {$title} CRUD pages:", 'cyan');
            
            // Test Create page
            $createUrl = $this->baseUrl . "/admin/student-apps/{$module}/create";
            $createResponse = $this->makeRequest($createUrl);
            
            if ($createResponse !== false) {
                CLI::write("     ✓ Create page accessible", 'green');
            } else {
                CLI::write("     ✗ Create page failed", 'red');
            }
            
            // Test Edit page (with dummy ID)
            $editUrl = $this->baseUrl . "/admin/student-apps/{$module}/edit/1";
            $editResponse = $this->makeRequest($editUrl);
            
            if ($editResponse !== false) {
                CLI::write("     ✓ Edit page accessible", 'green');
            } else {
                CLI::write("     ✗ Edit page failed", 'red');
            }
            
            // Test Show page (with dummy ID)
            $showUrl = $this->baseUrl . "/admin/student-apps/{$module}/show/1";
            $showResponse = $this->makeRequest($showUrl);
            
            if ($showResponse !== false) {
                CLI::write("     ✓ Show page accessible", 'green');
            } else {
                CLI::write("     ✗ Show page failed", 'red');
            }
            
            // Test AJAX getData endpoint
            $dataUrl = $this->baseUrl . "/admin/student-apps/{$module}/get-data";
            $dataResponse = $this->makeAjaxRequest($dataUrl);
            
            if ($dataResponse !== false) {
                CLI::write("     ✓ AJAX data endpoint accessible", 'green');
            } else {
                CLI::write("     ✗ AJAX data endpoint failed", 'red');
            }
        }
    }

    private function makeRequest($url)
    {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return false;
        }
        
        // Check if response contains error indicators
        if (strpos($response, 'Fatal error') !== false || 
            strpos($response, 'Parse error') !== false ||
            strpos($response, '404') !== false) {
            return false;
        }
        
        return $response;
    }

    private function makeAjaxRequest($url)
    {
        $postData = http_build_query([
            'draw' => 1,
            'start' => 0,
            'length' => 10,
            'search' => ['value' => ''],
            'order' => [['column' => 0, 'dir' => 'asc']]
        ]);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'X-Requested-With: XMLHttpRequest'
                ],
                'content' => $postData,
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return false;
        }
        
        // Check if it's valid JSON response
        $json = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        
        return $response;
    }
}
