<?php

namespace App\Controllers;

use App\Models\StaffModel;
use App\Models\DepartmentModel;
use App\Models\StaffDesignationModel;

class StaffController extends BaseCrudController
{
    protected $departmentModel;
    protected $designationModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StaffModel();
        $this->departmentModel = new DepartmentModel();
        $this->designationModel = new StaffDesignationModel();
        
        $this->viewPath = 'admin/staff';
        $this->routePrefix = 'admin/staff';
        $this->entityName = 'Staff';
        $this->entityNamePlural = 'Staff';
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'departments' => $this->getDepartments(),
            'designations' => $this->getDesignations(),
            'genders' => [
                'Male' => 'Male',
                'Female' => 'Female',
                'Other' => 'Other'
            ],
            'marital_statuses' => [
                'Single' => 'Single',
                'Married' => 'Married',
                'Divorced' => 'Divorced',
                'Widowed' => 'Widowed'
            ],
            'contract_types' => [
                'Permanent' => 'Permanent',
                'Contract' => 'Contract',
                'Part-time' => 'Part-time',
                'Temporary' => 'Temporary'
            ]
        ];
    }

    /**
     * Get departments for dropdown
     */
    private function getDepartments()
    {
        try {
            return $this->departmentModel->getForDropdown();
        } catch (\Exception $e) {
            return [
                1 => 'Administration',
                2 => 'Teaching',
                3 => 'Support Staff',
                4 => 'Management'
            ];
        }
    }

    /**
     * Get designations for dropdown
     */
    private function getDesignations()
    {
        try {
            return $this->designationModel->getForDropdown();
        } catch (\Exception $e) {
            return [
                1 => 'Principal',
                2 => 'Vice Principal',
                3 => 'Teacher',
                4 => 'Assistant Teacher',
                5 => 'Clerk',
                6 => 'Peon'
            ];
        }
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Generate employee ID if not provided
        if (empty($data['employee_id'])) {
            $data['employee_id'] = $this->model->generateEmployeeId();
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['lang_id'] = $data['lang_id'] ?? 1;
        $data['currency_id'] = $data['currency_id'] ?? 0;

        // Hash password if provided
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // Remove password field if empty (for updates)
            unset($data['password']);
        }

        return $data;
    }

    /**
     * Process file uploads
     */
    protected function processFileUploads($data, $id = null)
    {
        $uploadPath = WRITEPATH . 'uploads/staff/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Handle staff image
        $imageFile = $this->request->getFile('image');
        if ($imageFile && $imageFile->isValid() && !$imageFile->hasMoved()) {
            $newName = $imageFile->getRandomName();
            $imageFile->move($uploadPath, $newName);
            $data['image'] = $newName;
        }

        // Handle resume
        $resumeFile = $this->request->getFile('resume');
        if ($resumeFile && $resumeFile->isValid() && !$resumeFile->hasMoved()) {
            $newName = $resumeFile->getRandomName();
            $resumeFile->move($uploadPath, $newName);
            $data['resume'] = $newName;
        }

        // Handle joining letter
        $joiningLetterFile = $this->request->getFile('joining_letter');
        if ($joiningLetterFile && $joiningLetterFile->isValid() && !$joiningLetterFile->hasMoved()) {
            $newName = $joiningLetterFile->getRandomName();
            $joiningLetterFile->move($uploadPath, $newName);
            $data['joining_letter'] = $newName;
        }

        // Handle resignation letter
        $resignationLetterFile = $this->request->getFile('resignation_letter');
        if ($resignationLetterFile && $resignationLetterFile->isValid() && !$resignationLetterFile->hasMoved()) {
            $newName = $resignationLetterFile->getRandomName();
            $resignationLetterFile->move($uploadPath, $newName);
            $data['resignation_letter'] = $newName;
        }

        // Handle other document
        $otherDocumentFile = $this->request->getFile('other_document_file');
        if ($otherDocumentFile && $otherDocumentFile->isValid() && !$otherDocumentFile->hasMoved()) {
            $newName = $otherDocumentFile->getRandomName();
            $otherDocumentFile->move($uploadPath, $newName);
            $data['other_document_file'] = $newName;
        }

        return $data;
    }

    /**
     * Get relationships to load
     */
    protected function getRelationships()
    {
        return ['department', 'designation', 'attendance'];
    }

    /**
     * Get staff by department (AJAX)
     */
    public function getByDepartment()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $departmentId = $this->request->getPost('department_id');
        $staff = $this->model->getByDepartment($departmentId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $staff
        ]);
    }

    /**
     * Get staff statistics
     */
    public function statistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Staff attendance page
     */
    public function attendance()
    {
        $data = [
            'title' => 'Staff Attendance',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->model->getForDropdown(),
            'attendance_types' => $this->getAttendanceTypes(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Attendance', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/attendance', $data);
    }

    /**
     * Get attendance types
     */
    private function getAttendanceTypes()
    {
        try {
            $db = \Config\Database::connect();
            $types = $db->table('staff_attendance_type')
                       ->where('is_active', 'yes')
                       ->get()
                       ->getResultArray();
            
            $dropdown = [];
            foreach ($types as $type) {
                $dropdown[$type['id']] = $type['type'];
            }
            
            return $dropdown;
        } catch (\Exception $e) {
            return [
                1 => 'Present',
                2 => 'Absent',
                3 => 'Late',
                4 => 'Half Day'
            ];
        }
    }

    /**
     * Mark staff attendance
     */
    public function markAttendance()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $attendanceData = $this->request->getPost('attendance');
        $date = $this->request->getPost('date');

        if (empty($attendanceData) || empty($date)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Attendance data and date are required'
            ]);
        }

        $db = \Config\Database::connect();
        $db->transStart();

        try {
            $successCount = 0;
            
            foreach ($attendanceData as $staffId => $attendanceTypeId) {
                // Check if attendance already exists for this date
                $existing = $db->table('staff_attendance')
                             ->where('staff_id', $staffId)
                             ->where('date', $date)
                             ->get()
                             ->getRowArray();

                $data = [
                    'staff_id' => $staffId,
                    'date' => $date,
                    'staff_attendance_type_id' => $attendanceTypeId,
                    'is_active' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                if ($existing) {
                    // Update existing attendance
                    $db->table('staff_attendance')
                       ->where('id', $existing['id'])
                       ->update($data);
                } else {
                    // Insert new attendance
                    $db->table('staff_attendance')->insert($data);
                }

                $successCount++;
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to mark attendance'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Attendance marked for $successCount staff members"
            ]);

        } catch (\Exception $e) {
            $db->transRollback();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error occurred while marking attendance: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get attendance report
     */
    public function attendanceReport()
    {
        $staffId = $this->request->getGet('staff_id');
        $month = $this->request->getGet('month') ?: date('m');
        $year = $this->request->getGet('year') ?: date('Y');

        if (!$staffId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Staff ID is required'
            ]);
        }

        $summary = $this->model->getAttendanceSummary($staffId, $month, $year);

        return $this->response->setJSON([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Import staff from CSV
     */
    public function import()
    {
        $data = [
            'title' => 'Import Staff',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Import', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/import', $data);
    }

    /**
     * Process CSV import
     */
    public function processImport()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $csvFile = $this->request->getFile('csv_file');
        
        if (!$csvFile || !$csvFile->isValid()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a valid CSV file'
            ]);
        }

        // Process CSV file
        $csvData = array_map('str_getcsv', file($csvFile->getTempName()));
        $headers = array_shift($csvData);

        $importData = [];
        $errors = [];

        foreach ($csvData as $index => $row) {
            $rowData = array_combine($headers, $row);
            
            // Validate required fields
            if (empty($rowData['name']) || empty($rowData['email'])) {
                $errors[] = "Row " . ($index + 2) . ": Name and email are required";
                continue;
            }

            // Generate employee ID if not provided
            if (empty($rowData['employee_id'])) {
                $rowData['employee_id'] = $this->model->generateEmployeeId();
            }

            $importData[] = $rowData;
        }

        if (!empty($errors)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Import failed due to validation errors',
                'errors' => $errors
            ]);
        }

        // Bulk insert
        $result = $this->model->bulkInsert($importData);

        return $this->response->setJSON($result);
    }
}
