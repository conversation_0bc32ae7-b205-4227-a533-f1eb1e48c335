<?php

namespace App\Controllers;

use App\Models\StudentBehaviourModel;

class StudentBehaviourController extends BaseCrudController
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentBehaviourModel();
        
        $this->viewPath = 'admin/student_apps/behaviour';
        $this->routePrefix = 'admin/student-apps/behaviour';
        $this->entityName = 'Student Behaviour';
        $this->entityNamePlural = 'Student Behaviours';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $pointRange = $this->request->getPost('point_range');

        $builder = $this->model;

        // Apply point range filter
        if (!empty($pointRange)) {
            switch ($pointRange) {
                case 'positive':
                    $builder = $builder->where('point >', 0);
                    break;
                case 'negative':
                    $builder = $builder->where('point <', 0);
                    break;
                case 'neutral':
                    $builder = $builder->where('point', 0);
                    break;
            }
        }

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('title', $searchValue)
                   ->orLike('description', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'title', 'point', 'created_at'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->findAll();

        $data = [];
        foreach ($records as $record) {
            $pointBadge = $this->getPointBadge($record['point']);
            $impactLevel = $this->model->getBehaviourImpact($record['point']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'title' => $record['title'],
                'description' => substr($record['description'], 0, 100) . (strlen($record['description']) > 100 ? '...' : ''),
                'point' => $pointBadge,
                'impact_level' => '<span class="inline-flex items-center rounded-full bg-' . $impactLevel['color'] . ' bg-opacity-10 py-1 px-3 text-sm font-medium text-' . $impactLevel['color'] . '">
                                    <i class="' . $impactLevel['icon'] . ' mr-1"></i>
                                    ' . $impactLevel['level'] . '
                                  </span>',
                'created_at' => date('M j, Y', strtotime($record['created_at'])),
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get point badge HTML
     */
    private function getPointBadge($point)
    {
        $color = 'gray';
        $sign = '';
        
        if ($point > 0) {
            $color = 'success';
            $sign = '+';
        } elseif ($point < 0) {
            $color = 'danger';
        } else {
            $color = 'secondary';
        }

        return '<span class="inline-flex rounded-full bg-' . $color . ' bg-opacity-10 py-1 px-3 text-sm font-medium text-' . $color . '">' . $sign . $point . '</span>';
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
