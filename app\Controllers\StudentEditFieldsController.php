<?php

namespace App\Controllers;

use App\Models\StudentEditFieldsModel;

class StudentEditFieldsController extends BaseCrudController
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentEditFieldsModel();
        
        $this->viewPath = 'admin/student_apps/edit_fields';
        $this->routePrefix = 'admin/student-apps/edit-fields';
        $this->entityName = 'Student Edit Field';
        $this->entityNamePlural = 'Student Edit Fields';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'field_types' => $this->model->getFieldTypes(),
            'status_options' => $this->model->getStatusOptions()
        ];
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $statusFilter = $this->request->getPost('status');

        $builder = $this->model;

        // Apply status filter
        if (!empty($statusFilter)) {
            $builder = $builder->where('status', $statusFilter);
        }

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('name', $searchValue)
                   ->orLike('status', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'name', 'status', 'created_at'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->findAll();

        $data = [];
        foreach ($records as $record) {
            $statusBadge = $this->getStatusBadge($record['status']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'name' => $record['name'],
                'status' => $statusBadge,
                'created_at' => date('M j, Y', strtotime($record['created_at'])),
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Toggle field status
     */
    public function toggleStatus($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->toggleFieldStatus($id);

        return $this->response->setJSON($result);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get status badge HTML
     */
    private function getStatusBadge($status)
    {
        switch ($status) {
            case 'active':
                return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Active</span>';
            case 'inactive':
                return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Inactive</span>';
            default:
                return '<span class="inline-flex rounded-full bg-gray bg-opacity-10 py-1 px-3 text-sm font-medium text-gray">Unknown</span>';
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<button onclick="toggleStatus(' . $record['id'] . ')" class="hover:text-primary" title="Toggle Status">
                        <i class="fas fa-toggle-' . ($record['status'] === 'active' ? 'on' : 'off') . '"></i>
                    </button>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
