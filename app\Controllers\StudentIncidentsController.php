<?php

namespace App\Controllers;

use App\Models\StudentIncidentsModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;
use App\Models\StudentBehaviourModel;

class StudentIncidentsController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;
    protected $behaviourModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentIncidentsModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        $this->behaviourModel = new StudentBehaviourModel();
        
        $this->viewPath = 'admin/student_apps/incidents';
        $this->routePrefix = 'admin/student-apps/incidents';
        $this->entityName = 'Student Incident';
        $this->entityNamePlural = 'Student Incidents';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'student_sessions' => $this->getStudentSessionsForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'behaviours' => $this->behaviourModel->getForDropdown()
        ];
    }

    /**
     * Get student sessions for dropdown
     */
    private function getStudentSessionsForDropdown()
    {
        // Simplified version without complex joins
        $sessions = $this->studentSessionModel->where('is_active', 'yes')->findAll();
        $dropdown = [];

        foreach ($sessions as $session) {
            // Get student details separately
            $student = $this->studentsModel->find($session['student_id']);
            if ($student) {
                $label = $student['firstname'] . ' ' . $student['lastname'] . ' (' . $student['admission_no'] . ')';
                $dropdown[$session['id']] = $label;
            }
        }

        return $dropdown;
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'from_date' => $this->request->getPost('from_date'),
            'to_date' => $this->request->getPost('to_date')
        ];

        // Simplified approach - get incidents directly
        $builder = $this->model->builder();

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('title', $searchValue)
                   ->orLike('description', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'student_session_id', 'title', 'incident_date'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $actions = $this->getActionButtons($record);

            // Get student details separately
            $studentSession = $this->studentSessionModel->find($record['student_session_id']);
            $student = $studentSession ? $this->studentsModel->find($studentSession['student_id']) : null;

            $data[] = [
                'id' => $record['id'],
                'student_name' => $student ? $student['firstname'] . ' ' . $student['lastname'] : 'Unknown',
                'admission_no' => $student ? $student['admission_no'] : 'N/A',
                'class_section' => 'N/A', // Will be populated later if needed
                'title' => $record['title'],
                'description' => substr($record['description'], 0, 100) . (strlen($record['description']) > 100 ? '...' : ''),
                'incident_date' => date('M j, Y', strtotime($record['incident_date'])),
                'point' => $record['point'] ? ($record['point'] > 0 ? '+' : '') . $record['point'] : '0',
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
