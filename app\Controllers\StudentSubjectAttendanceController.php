<?php

namespace App\Controllers;

use App\Models\StudentSubjectAttendanceModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;
use App\Models\SubjectsModel;

class StudentSubjectAttendanceController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;
    protected $subjectsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentSubjectAttendanceModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        $this->subjectsModel = new SubjectsModel();
        
        $this->viewPath = 'admin/student_apps/subject_attendance';
        $this->routePrefix = 'admin/student-apps/subject-attendance';
        $this->entityName = 'Student Subject Attendance';
        $this->entityNamePlural = 'Student Subject Attendance';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'subjects' => $this->subjectsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'student_sessions' => $this->getStudentSessionsForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'subjects' => $this->subjectsModel->getForDropdown(),
            'attendance_types' => $this->model->getAttendanceTypes()
        ];
    }

    /**
     * Get student sessions for dropdown
     */
    private function getStudentSessionsForDropdown()
    {
        $sessions = $this->studentSessionModel->getStudentSessionWithDetails();
        $dropdown = [];
        
        foreach ($sessions as $session) {
            $label = $session['firstname'] . ' ' . $session['lastname'] . ' (' . $session['admission_no'] . ') - ' . $session['class'] . ' ' . $session['section'];
            $dropdown[$session['id']] = $label;
        }
        
        return $dropdown;
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'subject_id' => $this->request->getPost('subject_id'),
            'date' => $this->request->getPost('date'),
            'attendance_type' => $this->request->getPost('attendance_type')
        ];

        $builder = $this->model->getSubjectAttendanceWithDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->orLike('subjects.name', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'subject_name', 'date', 'attendence_type'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $attendanceType = $this->getAttendanceTypeBadge($record['attendence_type']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'class_section' => $record['class'] . ' - ' . $record['section'],
                'subject' => $record['subject_name'],
                'date' => date('M j, Y', strtotime($record['date'])),
                'attendance_type' => $attendanceType,
                'remark' => $record['remark'] ?? '-',
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Mark attendance for multiple students
     */
    public function markAttendance()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->markBulkSubjectAttendance($data);

        return $this->response->setJSON($result);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get attendance type badge HTML
     */
    private function getAttendanceTypeBadge($type)
    {
        switch ($type) {
            case 'Present':
                return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Present</span>';
            case 'Late':
                return '<span class="inline-flex rounded-full bg-warning bg-opacity-10 py-1 px-3 text-sm font-medium text-warning">Late</span>';
            case 'Absent':
                return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Absent</span>';
            case 'Half Day':
                return '<span class="inline-flex rounded-full bg-info bg-opacity-10 py-1 px-3 text-sm font-medium text-info">Half Day</span>';
            default:
                return '<span class="inline-flex rounded-full bg-gray bg-opacity-10 py-1 px-3 text-sm font-medium text-gray">Unknown</span>';
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
