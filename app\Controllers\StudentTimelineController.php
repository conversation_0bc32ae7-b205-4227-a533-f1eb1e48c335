<?php

namespace App\Controllers;

use App\Models\StudentTimelineModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;

class StudentTimelineController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentTimelineModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        
        $this->viewPath = 'admin/student_apps/timeline';
        $this->routePrefix = 'admin/student-apps/timeline';
        $this->entityName = 'Student Timeline';
        $this->entityNamePlural = 'Student Timeline';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'students' => $this->studentsModel->getForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'statuses' => $this->model->getCommonStatuses(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'students' => $this->studentsModel->getForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'statuses' => $this->model->getCommonStatuses(),
            'timeline_types' => $this->model->getTimelineTypes()
        ];
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'student_id' => $this->request->getPost('student_id'),
            'status' => $this->request->getPost('status'),
            'from_date' => $this->request->getPost('from_date'),
            'to_date' => $this->request->getPost('to_date')
        ];

        $builder = $this->model->getTimelineWithStudentDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->orLike('student_timeline.title', $searchValue)
                   ->orLike('student_timeline.description', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'title', 'timeline_date', 'status'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $statusBadge = $this->getStatusBadge($record['status']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'title' => $record['title'],
                'description' => substr($record['description'], 0, 100) . (strlen($record['description']) > 100 ? '...' : ''),
                'timeline_date' => date('M j, Y', strtotime($record['timeline_date'])),
                'status' => $statusBadge,
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get status badge HTML
     */
    private function getStatusBadge($status)
    {
        $statusColors = [
            'Active' => 'success',
            'Completed' => 'primary',
            'Pending' => 'warning',
            'In Progress' => 'info',
            'Cancelled' => 'danger',
            'On Hold' => 'secondary',
            'Approved' => 'success',
            'Rejected' => 'danger',
            'Under Review' => 'warning'
        ];

        $color = $statusColors[$status] ?? 'gray';
        return '<span class="inline-flex rounded-full bg-' . $color . ' bg-opacity-10 py-1 px-3 text-sm font-medium text-' . $color . '">' . $status . '</span>';
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
