<?php

namespace App\Controllers;

use App\Models\StudentTransportFeesModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;

class StudentTransportFeesController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentTransportFeesModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        
        $this->viewPath = 'admin/student_apps/transport_fees';
        $this->routePrefix = 'admin/student-apps/transport-fees';
        $this->entityName = 'Student Transport Fee';
        $this->entityNamePlural = 'Student Transport Fees';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'student_sessions' => $this->getStudentSessionsForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'transport_routes' => $this->model->getTransportRoutes(),
            'payment_modes' => $this->model->getPaymentModes()
        ];
    }

    /**
     * Get student sessions for dropdown
     */
    private function getStudentSessionsForDropdown()
    {
        $sessions = $this->studentSessionModel->getStudentSessionWithDetails();
        $dropdown = [];
        
        foreach ($sessions as $session) {
            $label = $session['firstname'] . ' ' . $session['lastname'] . ' (' . $session['admission_no'] . ') - ' . $session['class'] . ' ' . $session['section'];
            $dropdown[$session['id']] = $label;
        }
        
        return $dropdown;
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'payment_status' => $this->request->getPost('payment_status'),
            'month' => $this->request->getPost('month'),
            'year' => $this->request->getPost('year')
        ];

        $builder = $this->model->getTransportFeesWithStudentDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'amount', 'amount_paid', 'due_date'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $paymentStatus = $this->getPaymentStatusBadge($record);
            $actions = $this->getActionButtons($record);

            $balanceAmount = ($record['amount'] ?? 0) - ($record['amount_paid'] ?? 0);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'class_section' => $record['class'] . ' - ' . $record['section'],
                'month_year' => date('M Y', strtotime($record['month'] . '-01')),
                'amount' => number_format($record['amount'] ?? 0, 2),
                'paid_amount' => number_format($record['amount_paid'] ?? 0, 2),
                'balance' => number_format($balanceAmount, 2),
                'due_date' => $record['due_date'] ? date('M j, Y', strtotime($record['due_date'])) : '-',
                'payment_status' => $paymentStatus,
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get payment status badge HTML
     */
    private function getPaymentStatusBadge($record)
    {
        $amount = $record['amount'] ?? 0;
        $paidAmount = $record['amount_paid'] ?? 0;
        $balanceAmount = $amount - $paidAmount;

        if ($balanceAmount <= 0) {
            return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Paid</span>';
        } elseif ($paidAmount > 0) {
            return '<span class="inline-flex rounded-full bg-warning bg-opacity-10 py-1 px-3 text-sm font-medium text-warning">Partial</span>';
        } else {
            $dueDate = $record['due_date'] ?? null;
            if ($dueDate && strtotime($dueDate) < time()) {
                return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Overdue</span>';
            } else {
                return '<span class="inline-flex rounded-full bg-info bg-opacity-10 py-1 px-3 text-sm font-medium text-info">Pending</span>';
            }
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
