<?php

namespace App\Controllers;

use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;
use App\Models\SessionsModel;
use App\Models\StudentSessionModel;
use App\Models\StudentAttendanceModel;
use App\Models\StudentFeesModel;

class StudentsController extends BaseCrudController
{
    protected $classesModel;
    protected $sectionsModel;
    protected $sessionsModel;
    protected $studentSessionModel;
    protected $studentAttendanceModel;
    protected $studentFeesModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        $this->sessionsModel = new SessionsModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentAttendanceModel = new StudentAttendanceModel();
        $this->studentFeesModel = new StudentFeesModel();
        
        $this->viewPath = 'admin/students';
        $this->routePrefix = 'admin/students';
        $this->entityName = 'Student';
        $this->entityNamePlural = 'Students';
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'blood_groups' => [
                'A+' => 'A+', 'A-' => 'A-', 'B+' => 'B+', 'B-' => 'B-',
                'AB+' => 'AB+', 'AB-' => 'AB-', 'O+' => 'O+', 'O-' => 'O-'
            ],
            'genders' => [
                'Male' => 'Male',
                'Female' => 'Female',
                'Other' => 'Other'
            ],
            'guardian_relations' => [
                'Father' => 'Father',
                'Mother' => 'Mother',
                'Guardian' => 'Guardian',
                'Other' => 'Other'
            ]
        ];
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Generate admission number if not provided
        if (empty($data['admission_no'])) {
            $data['admission_no'] = $this->model->generateAdmissionNo();
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['admission_date'] = $data['admission_date'] ?? date('Y-m-d');

        // Process guardian information
        if ($data['guardian_is'] === 'father') {
            $data['guardian_name'] = $data['father_name'] ?? '';
            $data['guardian_phone'] = $data['father_phone'] ?? '';
            $data['guardian_occupation'] = $data['father_occupation'] ?? '';
        } elseif ($data['guardian_is'] === 'mother') {
            $data['guardian_name'] = $data['mother_name'] ?? '';
            $data['guardian_phone'] = $data['mother_phone'] ?? '';
            $data['guardian_occupation'] = $data['mother_occupation'] ?? '';
        }

        return $data;
    }

    /**
     * Process file uploads
     */
    protected function processFileUploads($data, $id = null)
    {
        $uploadPath = WRITEPATH . 'uploads/students/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Handle student image
        $imageFile = $this->request->getFile('image');
        if ($imageFile && $imageFile->isValid() && !$imageFile->hasMoved()) {
            $newName = $imageFile->getRandomName();
            $imageFile->move($uploadPath, $newName);
            $data['image'] = $newName;
        }

        // Handle father picture
        $fatherPicFile = $this->request->getFile('father_pic');
        if ($fatherPicFile && $fatherPicFile->isValid() && !$fatherPicFile->hasMoved()) {
            $newName = $fatherPicFile->getRandomName();
            $fatherPicFile->move($uploadPath, $newName);
            $data['father_pic'] = $newName;
        }

        // Handle mother picture
        $motherPicFile = $this->request->getFile('mother_pic');
        if ($motherPicFile && $motherPicFile->isValid() && !$motherPicFile->hasMoved()) {
            $newName = $motherPicFile->getRandomName();
            $motherPicFile->move($uploadPath, $newName);
            $data['mother_pic'] = $newName;
        }

        // Handle guardian picture
        $guardianPicFile = $this->request->getFile('guardian_pic');
        if ($guardianPicFile && $guardianPicFile->isValid() && !$guardianPicFile->hasMoved()) {
            $newName = $guardianPicFile->getRandomName();
            $guardianPicFile->move($uploadPath, $newName);
            $data['guardian_pic'] = $newName;
        }

        return $data;
    }

    /**
     * Get relationships to load
     */
    protected function getRelationships()
    {
        return ['session', 'fees'];
    }

    /**
     * Get students by class and section (AJAX)
     */
    public function getByClassSection()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $classId = $this->request->getPost('class_id');
        $sectionId = $this->request->getPost('section_id');
        $sessionId = $this->request->getPost('session_id');

        $students = $this->model->getByClassSection($classId, $sectionId, $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $students
        ]);
    }

    /**
     * Search students (AJAX)
     */
    public function search()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $searchTerm = $this->request->getPost('search');
        $filters = $this->request->getPost('filters') ?? [];

        $students = $this->model->searchStudents($searchTerm, $filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $students
        ]);
    }

    /**
     * Get student statistics
     */
    public function statistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Promote students to next class
     */
    public function promote()
    {
        $data = [
            'title' => 'Promote Students',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Promote', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/promote', $data);
    }

    /**
     * Process student promotion
     */
    public function processPromotion()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $fromClassId = $this->request->getPost('from_class_id');
        $fromSectionId = $this->request->getPost('from_section_id');
        $fromSessionId = $this->request->getPost('from_session_id');
        $toClassId = $this->request->getPost('to_class_id');
        $toSectionId = $this->request->getPost('to_section_id');
        $toSessionId = $this->request->getPost('to_session_id');
        $studentIds = $this->request->getPost('student_ids');

        if (empty($studentIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No students selected for promotion'
            ]);
        }

        // Use StudentSessionModel for promotion
        $result = $this->studentSessionModel->promoteStudents(
            $fromClassId, $fromSectionId, $fromSessionId,
            $toClassId, $toSectionId, $toSessionId,
            $studentIds
        );

        return $this->response->setJSON($result);
    }

    /**
     * Import students from CSV
     */
    public function import()
    {
        $data = [
            'title' => 'Import Students',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Import', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/import', $data);
    }

    /**
     * Process CSV import
     */
    public function processImport()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $csvFile = $this->request->getFile('csv_file');
        
        if (!$csvFile || !$csvFile->isValid()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a valid CSV file'
            ]);
        }

        // Process CSV file
        $csvData = array_map('str_getcsv', file($csvFile->getTempName()));
        $headers = array_shift($csvData);

        $importData = [];
        $errors = [];

        foreach ($csvData as $index => $row) {
            $rowData = array_combine($headers, $row);
            
            // Validate required fields
            if (empty($rowData['firstname']) || empty($rowData['lastname'])) {
                $errors[] = "Row " . ($index + 2) . ": First name and last name are required";
                continue;
            }

            // Generate admission number if not provided
            if (empty($rowData['admission_no'])) {
                $rowData['admission_no'] = $this->model->generateAdmissionNo();
            }

            $importData[] = $rowData;
        }

        if (!empty($errors)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Import failed due to validation errors',
                'errors' => $errors
            ]);
        }

        // Bulk insert
        $result = $this->model->bulkInsert($importData);

        return $this->response->setJSON($result);
    }

    /**
     * Student session management
     */
    public function sessions($studentId)
    {
        $student = $this->model->find($studentId);

        if (!$student) {
            return redirect()->to($this->routePrefix)->with('error', 'Student not found');
        }

        $data = [
            'title' => 'Student Sessions - ' . $student['firstname'] . ' ' . $student['lastname'],
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'student' => $student,
            'sessions' => $this->studentSessionModel->getStudentSessionWithDetails($studentId),
            'available_sessions' => $this->sessionsModel->getForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Sessions', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/sessions', $data);
    }

    /**
     * Enroll student in session
     */
    public function enrollSession()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->studentSessionModel->enrollStudent($data);

        return $this->response->setJSON($result);
    }

    /**
     * Student attendance management
     */
    public function attendance($studentId = null)
    {
        $data = [
            'title' => 'Student Attendance',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'attendance_types' => $this->studentAttendanceModel->getAttendanceTypes(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Attendance', 'url' => '']
            ]
        ];

        if ($studentId) {
            $student = $this->model->find($studentId);
            if ($student) {
                $data['student'] = $student;
                $data['title'] = 'Attendance - ' . $student['firstname'] . ' ' . $student['lastname'];
                $data['attendance_summary'] = $this->studentAttendanceModel->getStudentAttendanceSummary($studentId);
            }
        }

        return view($this->viewPath . '/attendance', $data);
    }

    /**
     * Mark student attendance
     */
    public function markAttendance()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $attendanceData = $this->request->getPost('attendance');
        $date = $this->request->getPost('date');

        if (empty($attendanceData) || empty($date)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Attendance data and date are required'
            ]);
        }

        $result = $this->studentAttendanceModel->markBulkAttendance($attendanceData, $date);

        return $this->response->setJSON($result);
    }

    /**
     * Get attendance report
     */
    public function attendanceReport()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'attendance_type_id' => $this->request->getPost('attendance_type_id')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        $attendance = $this->studentAttendanceModel->getAttendanceWithDetails($filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $attendance
        ]);
    }

    /**
     * Student fees management
     */
    public function fees($studentId = null)
    {
        $data = [
            'title' => 'Student Fees',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Fees', 'url' => '']
            ]
        ];

        if ($studentId) {
            $student = $this->model->find($studentId);
            if ($student) {
                $data['student'] = $student;
                $data['title'] = 'Fees - ' . $student['firstname'] . ' ' . $student['lastname'];
                $data['fee_summary'] = $this->studentFeesModel->getStudentFeeSummary($studentId);
            }
        }

        return view($this->viewPath . '/fees', $data);
    }

    /**
     * Get fee collection summary
     */
    public function feeCollectionSummary()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'session_id' => $this->request->getPost('session_id')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        $summary = $this->studentFeesModel->getFeeCollectionSummary($filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Get defaulters list
     */
    public function defaulters()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        $defaulters = $this->studentFeesModel->getDefaulters($filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $defaulters
        ]);
    }
}
