<?php

namespace App\Controllers;

use App\Models\StudentsModel;
use App\Models\StudentSessionModel;
use App\Models\StudentAttendanceModel;
use App\Models\StudentFeesModel;

class TestController extends BaseController
{
    public function index()
    {
        $results = [];
        
        try {
            // Test StudentsModel
            $studentsModel = new StudentsModel();
            $results['students_count'] = $studentsModel->countAllResults();
            $results['students_test'] = 'StudentsModel working';
        } catch (\Exception $e) {
            $results['students_error'] = $e->getMessage();
        }

        try {
            // Test StudentSessionModel
            $sessionModel = new StudentSessionModel();
            $results['sessions_count'] = $sessionModel->countAllResults();
            $results['sessions_test'] = 'StudentSessionModel working';
        } catch (\Exception $e) {
            $results['sessions_error'] = $e->getMessage();
        }

        try {
            // Test StudentAttendanceModel
            $attendanceModel = new StudentAttendanceModel();
            $results['attendance_count'] = $attendanceModel->countAllResults();
            $results['attendance_test'] = 'StudentAttendanceModel working';
        } catch (\Exception $e) {
            $results['attendance_error'] = $e->getMessage();
        }

        try {
            // Test StudentFeesModel
            $feesModel = new StudentFeesModel();
            $results['fees_count'] = $feesModel->countAllResults();
            $results['fees_test'] = 'StudentFeesModel working';
        } catch (\Exception $e) {
            $results['fees_error'] = $e->getMessage();
        }

        // Test database connection
        try {
            $db = \Config\Database::connect();
            $results['db_test'] = 'Database connection working';
            
            // Test if tables exist
            $tables = $db->listTables();
            $results['tables_count'] = count($tables);
            $results['tables'] = $tables;
        } catch (\Exception $e) {
            $results['db_error'] = $e->getMessage();
        }

        return $this->response->setJSON($results);
    }

    public function createSampleData()
    {
        $results = [];
        
        try {
            $studentsModel = new StudentsModel();
            
            // Create sample student
            $sampleStudent = [
                'firstname' => 'John',
                'lastname' => 'Doe',
                'admission_no' => 'STU2025001',
                'email' => '<EMAIL>',
                'mobileno' => '1234567890',
                'gender' => 'Male',
                'dob' => '2010-01-15',
                'admission_date' => date('Y-m-d'),
                'is_active' => 'yes'
            ];
            
            $result = $studentsModel->createRecord($sampleStudent);
            $results['sample_student'] = $result;
            
        } catch (\Exception $e) {
            $results['sample_error'] = $e->getMessage();
        }

        return $this->response->setJSON($results);
    }
}
