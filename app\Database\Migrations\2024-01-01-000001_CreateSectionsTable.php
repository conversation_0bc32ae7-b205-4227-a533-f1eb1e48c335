<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSectionsTable extends Migration
{
    public function up()
    {
        // Check if table already exists
        $db = \Config\Database::connect();
        if ($db->tableExists('sections')) {
            return;
        }

        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'section' => [
                'type'       => 'VARCHAR',
                'constraint' => 60,
                'null'       => false,
            ],
            'is_active' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'default'    => 'no',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATE',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('section');
        $this->forge->createTable('sections');

        // Insert default sections
        $data = [
            ['section' => 'A', 'is_active' => 'yes'],
            ['section' => 'B', 'is_active' => 'yes'],
            ['section' => 'C', 'is_active' => 'yes'],
            ['section' => 'D', 'is_active' => 'yes'],
        ];

        $db->table('sections')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('sections');
    }
}
