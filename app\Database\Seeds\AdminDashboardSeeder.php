<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AdminDashboardSeeder extends Seeder
{
    public function run()
    {
        // Check if tables exist before seeding
        $db = \Config\Database::connect();
        
        // Seed Students table if it exists and is empty
        if ($db->tableExists('students')) {
            $studentCount = $db->table('students')->countAllResults();
            if ($studentCount == 0) {
                $this->seedStudents($db);
            }
        }
        
        // Seed Staff table if it exists and is empty
        if ($db->tableExists('staff')) {
            $staffCount = $db->table('staff')->countAllResults();
            if ($staffCount == 0) {
                $this->seedStaff($db);
            }
        }
        
        // Seed Classes table if it exists and is empty
        if ($db->tableExists('classes')) {
            $classCount = $db->table('classes')->countAllResults();
            if ($classCount == 0) {
                $this->seedClasses($db);
            }
        }
        
        // Seed Expenses if tables exist
        if ($db->tableExists('expense_head') && $db->tableExists('expenses')) {
            $this->seedExpenses($db);
        }
    }
    
    private function seedStudents($db)
    {
        $students = [
            [
                'admission_no' => 'STU001',
                'firstname' => 'John',
                'lastname' => 'Doe',
                'email' => '<EMAIL>',
                'mobileno' => '1234567890',
                'father_name' => 'Robert Doe',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-30 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'admission_no' => 'STU002',
                'firstname' => 'Jane',
                'lastname' => 'Smith',
                'email' => '<EMAIL>',
                'mobileno' => '0987654321',
                'father_name' => 'Michael Smith',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-25 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'admission_no' => 'STU003',
                'firstname' => 'Alice',
                'lastname' => 'Johnson',
                'email' => '<EMAIL>',
                'mobileno' => '5555555555',
                'father_name' => 'David Johnson',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-20 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        $db->table('students')->insertBatch($students);
        echo "Seeded students table with sample data.\n";
    }
    
    private function seedStaff($db)
    {
        $staff = [
            [
                'employee_id' => 'EMP001',
                'name' => 'Dr. Sarah Wilson',
                'email' => '<EMAIL>',
                'contact_no' => '1111111111',
                'designation' => 'Principal',
                'department' => 'Administration',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-60 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'employee_id' => 'EMP002',
                'name' => 'Mr. James Brown',
                'email' => '<EMAIL>',
                'contact_no' => '2222222222',
                'designation' => 'Teacher',
                'department' => 'Mathematics',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-55 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'employee_id' => 'EMP003',
                'name' => 'Ms. Emily Davis',
                'email' => '<EMAIL>',
                'contact_no' => '3333333333',
                'designation' => 'Teacher',
                'department' => 'English',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-50 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        $db->table('staff')->insertBatch($staff);
        echo "Seeded staff table with sample data.\n";
    }
    
    private function seedClasses($db)
    {
        $classes = [
            [
                'class' => '1st Grade',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-90 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'class' => '2nd Grade',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-90 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'class' => '3rd Grade',
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s', strtotime('-90 days')),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        $db->table('classes')->insertBatch($classes);
        echo "Seeded classes table with sample data.\n";
    }
    
    private function seedExpenses($db)
    {
        // First check if expense_head has data
        $expenseHeadCount = $db->table('expense_head')->countAllResults();
        if ($expenseHeadCount == 0) {
            $expenseHeads = [
                [
                    'exp_category' => 'Office Supplies',
                    'description' => 'Office supplies and stationery',
                    'is_active' => 'yes',
                    'created_at' => date('Y-m-d H:i:s')
                ],
                [
                    'exp_category' => 'Utilities',
                    'description' => 'Electricity, water, internet',
                    'is_active' => 'yes',
                    'created_at' => date('Y-m-d H:i:s')
                ],
                [
                    'exp_category' => 'Maintenance',
                    'description' => 'Building and equipment maintenance',
                    'is_active' => 'yes',
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];
            
            $db->table('expense_head')->insertBatch($expenseHeads);
            echo "Seeded expense_head table with sample data.\n";
        }
        
        // Now seed expenses
        $expenseCount = $db->table('expenses')->countAllResults();
        if ($expenseCount == 0) {
            $expenses = [
                [
                    'exp_head_id' => 1,
                    'name' => 'Office Stationery Purchase',
                    'invoice_no' => 'INV001',
                    'date' => date('Y-m-d', strtotime('-15 days')),
                    'amount' => 250.00,
                    'note' => 'Monthly stationery supplies',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-15 days'))
                ],
                [
                    'exp_head_id' => 2,
                    'name' => 'Electricity Bill',
                    'invoice_no' => 'INV002',
                    'date' => date('Y-m-d', strtotime('-10 days')),
                    'amount' => 450.00,
                    'note' => 'Monthly electricity bill',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-10 days'))
                ],
                [
                    'exp_head_id' => 3,
                    'name' => 'AC Maintenance',
                    'invoice_no' => 'INV003',
                    'date' => date('Y-m-d', strtotime('-5 days')),
                    'amount' => 180.00,
                    'note' => 'Air conditioning maintenance',
                    'created_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
                ]
            ];
            
            $db->table('expenses')->insertBatch($expenses);
            echo "Seeded expenses table with sample data.\n";
        }
    }
}
