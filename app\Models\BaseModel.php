<?php

namespace App\Models;

use CodeIgniter\Model;

class BaseModel extends Model
{
    protected $db;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    public function __construct()
    {
        parent::__construct();
        $this->db = \Config\Database::connect();
    }

    /**
     * Get all records with optional filters
     */
    public function getAll($filters = [], $limit = null, $offset = 0, $orderBy = null)
    {
        $builder = $this->builder();
        
        // Apply filters
        if (!empty($filters)) {
            foreach ($filters as $field => $value) {
                if (is_array($value)) {
                    $builder->whereIn($field, $value);
                } else {
                    $builder->where($field, $value);
                }
            }
        }
        
        // Apply ordering
        if ($orderBy) {
            if (is_array($orderBy)) {
                foreach ($orderBy as $field => $direction) {
                    $builder->orderBy($field, $direction);
                }
            } else {
                $builder->orderBy($orderBy);
            }
        } else {
            $builder->orderBy($this->primaryKey, 'DESC');
        }
        
        // Apply pagination
        if ($limit) {
            $builder->limit($limit, $offset);
        }
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get record by ID with relationships
     */
    public function getById($id, $with = [])
    {
        $record = $this->find($id);
        
        if ($record && !empty($with)) {
            foreach ($with as $relation) {
                $record = $this->loadRelation($record, $relation);
            }
        }
        
        return $record;
    }

    /**
     * Create new record with validation
     */
    public function createRecord($data)
    {
        // Validate data
        if (!$this->validate($data)) {
            return [
                'success' => false,
                'errors' => $this->errors(),
                'data' => null
            ];
        }
        
        // Insert record
        $id = $this->insert($data);
        
        if ($id) {
            return [
                'success' => true,
                'errors' => [],
                'data' => $this->find($id)
            ];
        }
        
        return [
            'success' => false,
            'errors' => ['Database error occurred'],
            'data' => null
        ];
    }

    /**
     * Update record with validation
     */
    public function updateRecord($id, $data)
    {
        // Check if record exists
        if (!$this->find($id)) {
            return [
                'success' => false,
                'errors' => ['Record not found'],
                'data' => null
            ];
        }
        
        // Validate data
        if (!$this->validate($data)) {
            return [
                'success' => false,
                'errors' => $this->errors(),
                'data' => null
            ];
        }
        
        // Update record
        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'errors' => [],
                'data' => $this->find($id)
            ];
        }
        
        return [
            'success' => false,
            'errors' => ['Database error occurred'],
            'data' => null
        ];
    }

    /**
     * Delete record
     */
    public function deleteRecord($id)
    {
        // Check if record exists
        if (!$this->find($id)) {
            return [
                'success' => false,
                'errors' => ['Record not found'],
                'data' => null
            ];
        }
        
        // Delete record
        if ($this->delete($id)) {
            return [
                'success' => true,
                'errors' => [],
                'data' => null
            ];
        }
        
        return [
            'success' => false,
            'errors' => ['Database error occurred'],
            'data' => null
        ];
    }

    /**
     * Get paginated data for DataTables
     */
    public function getDataTableData($request)
    {
        $builder = $this->builder();
        
        // Total records
        $totalRecords = $builder->countAllResults(false);
        
        // Search
        if (!empty($request['search']['value'])) {
            $searchValue = $request['search']['value'];
            $searchableColumns = $this->getSearchableColumns();
            
            if (!empty($searchableColumns)) {
                $builder->groupStart();
                foreach ($searchableColumns as $column) {
                    $builder->orLike($column, $searchValue);
                }
                $builder->groupEnd();
            }
        }
        
        // Filtered records count
        $filteredRecords = $builder->countAllResults(false);
        
        // Ordering
        if (!empty($request['order'])) {
            $orderColumnIndex = $request['order'][0]['column'];
            $orderDirection = $request['order'][0]['dir'];
            $orderableColumns = $this->getOrderableColumns();
            
            if (isset($orderableColumns[$orderColumnIndex])) {
                $builder->orderBy($orderableColumns[$orderColumnIndex], $orderDirection);
            }
        }
        
        // Pagination
        $start = $request['start'] ?? 0;
        $length = $request['length'] ?? 10;
        $builder->limit($length, $start);
        
        $data = $builder->get()->getResultArray();
        
        return [
            'draw' => intval($request['draw'] ?? 1),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ];
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->allowedFields ?? [];
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->allowedFields ?? [];
    }

    /**
     * Load relationship data
     */
    protected function loadRelation($record, $relation)
    {
        // This method should be overridden in child models
        // to implement specific relationship loading logic
        return $record;
    }

    /**
     * Bulk insert records
     */
    public function bulkInsert($data)
    {
        if (empty($data)) {
            return [
                'success' => false,
                'errors' => ['No data provided'],
                'data' => null
            ];
        }
        
        $this->db->transStart();
        
        try {
            $this->insertBatch($data);
            $this->db->transComplete();
            
            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'errors' => ['Bulk insert failed'],
                    'data' => null
                ];
            }
            
            return [
                'success' => true,
                'errors' => [],
                'data' => ['inserted_count' => count($data)]
            ];
        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'errors' => [$e->getMessage()],
                'data' => null
            ];
        }
    }

    /**
     * Get active records only
     */
    public function getActive($filters = [])
    {
        $filters['is_active'] = 'yes';
        return $this->getAll($filters);
    }

    /**
     * Toggle active status
     */
    public function toggleStatus($id)
    {
        $record = $this->find($id);
        if (!$record) {
            return [
                'success' => false,
                'errors' => ['Record not found'],
                'data' => null
            ];
        }
        
        $newStatus = ($record['is_active'] === 'yes') ? 'no' : 'yes';
        
        return $this->updateRecord($id, ['is_active' => $newStatus]);
    }
}
