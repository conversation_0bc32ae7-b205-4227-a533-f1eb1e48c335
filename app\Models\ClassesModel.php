<?php

namespace App\Models;

class ClassesModel extends BaseModel
{
    protected $table = 'classes';
    protected $primaryKey = 'id';
    protected $allowedFields = ['class', 'is_active'];

    protected $validationRules = [
        'class' => 'required|min_length[1]|max_length[60]|is_unique[classes.class,id,{id}]'
    ];

    protected $validationMessages = [
        'class' => [
            'required' => 'Class name is required',
            'min_length' => 'Class name must be at least 1 character long',
            'max_length' => 'Class name cannot exceed 60 characters',
            'is_unique' => 'Class name already exists'
        ]
    ];

    protected $searchableColumns = ['class'];
    protected $orderableColumns = ['id', 'class', 'is_active', 'created_at'];

    /**
     * Get classes with student count
     */
    public function getClassesWithStudentCount($sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('classes.*, COUNT(student_session.student_id) as student_count')
                ->join('student_session', 'classes.id = student_session.class_id', 'left');
        
        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }
        
        return $builder->groupBy('classes.id')
                      ->orderBy('classes.class', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get classes for dropdown
     */
    public function getForDropdown()
    {
        $classes = $this->where('is_active', 'yes')
                       ->orderBy('class', 'ASC')
                       ->findAll();
        
        $dropdown = [];
        foreach ($classes as $class) {
            $dropdown[$class['id']] = $class['class'];
        }
        
        return $dropdown;
    }

    /**
     * Get class sections
     */
    public function getClassSections($classId)
    {
        return $this->db->table('class_sections')
                       ->select('class_sections.*, sections.section')
                       ->join('sections', 'class_sections.section_id = sections.id')
                       ->where('class_sections.class_id', $classId)
                       ->where('class_sections.is_active', 'yes')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get class statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total classes
        $stats['total'] = $this->countAllResults();
        
        // Active classes
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Classes with students
        $stats['with_students'] = $this->db->table($this->table)
                                          ->join('student_session', 'classes.id = student_session.class_id')
                                          ->distinct()
                                          ->countAllResults();
        
        return $stats;
    }
}
