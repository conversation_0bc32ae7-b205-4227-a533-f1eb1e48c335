<?php

namespace App\Models;

class DepartmentModel extends BaseModel
{
    protected $table = 'department';
    protected $primaryKey = 'id';
    protected $allowedFields = ['department_name', 'is_active'];

    protected $validationRules = [
        'department_name' => 'required|min_length[2]|max_length[200]|is_unique[department.department_name,id,{id}]'
    ];

    protected $validationMessages = [
        'department_name' => [
            'required' => 'Department name is required',
            'min_length' => 'Department name must be at least 2 characters long',
            'max_length' => 'Department name cannot exceed 200 characters',
            'is_unique' => 'Department name already exists'
        ]
    ];

    protected $searchableColumns = ['department_name'];
    protected $orderableColumns = ['id', 'department_name', 'is_active', 'created_at'];

    /**
     * Get departments for dropdown
     */
    public function getForDropdown()
    {
        $departments = $this->where('is_active', 'yes')
                           ->orderBy('department_name', 'ASC')
                           ->findAll();
        
        $dropdown = [];
        foreach ($departments as $department) {
            $dropdown[$department['id']] = $department['department_name'];
        }
        
        return $dropdown;
    }

    /**
     * Get department statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total departments
        $stats['total'] = $this->countAllResults();
        
        // Active departments
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Departments with staff
        $stats['with_staff'] = $this->db->table($this->table)
                                       ->join('staff', 'department.id = staff.department')
                                       ->distinct()
                                       ->countAllResults();
        
        return $stats;
    }
}
