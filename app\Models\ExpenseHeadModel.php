<?php

namespace App\Models;

class ExpenseHeadModel extends BaseModel
{
    protected $table = 'expense_head';
    protected $primaryKey = 'id';
    protected $allowedFields = ['exp_category', 'description', 'is_active', 'is_deleted'];

    protected $validationRules = [
        'exp_category' => 'required|min_length[2]|max_length[50]|is_unique[expense_head.exp_category,id,{id}]'
    ];

    protected $validationMessages = [
        'exp_category' => [
            'required' => 'Expense category is required',
            'min_length' => 'Expense category must be at least 2 characters long',
            'max_length' => 'Expense category cannot exceed 50 characters',
            'is_unique' => 'Expense category already exists'
        ]
    ];

    protected $searchableColumns = ['exp_category', 'description'];
    protected $orderableColumns = ['id', 'exp_category', 'is_active', 'created_at'];

    /**
     * Get expense heads for dropdown
     */
    public function getForDropdown()
    {
        $expenseHeads = $this->where('is_active', 'yes')
                            ->where('is_deleted', 'no')
                            ->orderBy('exp_category', 'ASC')
                            ->findAll();
        
        $dropdown = [];
        foreach ($expenseHeads as $head) {
            $dropdown[$head['id']] = $head['exp_category'];
        }
        
        return $dropdown;
    }

    /**
     * Get expense head statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total expense heads
        $stats['total'] = $this->where('is_deleted', 'no')->countAllResults();
        
        // Active expense heads
        $stats['active'] = $this->where('is_active', 'yes')
                               ->where('is_deleted', 'no')
                               ->countAllResults();
        
        // Expense heads with expenses
        $stats['with_expenses'] = $this->db->table($this->table)
                                          ->join('expenses', 'expense_head.id = expenses.exp_head_id')
                                          ->where('expense_head.is_deleted', 'no')
                                          ->distinct()
                                          ->countAllResults();
        
        return $stats;
    }

    /**
     * Soft delete expense head
     */
    public function softDelete($id)
    {
        return $this->update($id, ['is_deleted' => 'yes']);
    }

    /**
     * Restore soft deleted expense head
     */
    public function restore($id)
    {
        return $this->update($id, ['is_deleted' => 'no']);
    }

    /**
     * Get deleted expense heads
     */
    public function getDeleted()
    {
        return $this->where('is_deleted', 'yes')
                   ->orderBy('updated_at', 'DESC')
                   ->findAll();
    }
}
