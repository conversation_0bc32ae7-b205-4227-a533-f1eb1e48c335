<?php

namespace App\Models;

class ExpensesModel extends BaseModel
{
    protected $table = 'expenses';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'exp_head_id', 'name', 'invoice_no', 'date', 'amount', 'documents', 
        'note', 'is_active', 'is_deleted'
    ];

    protected $validationRules = [
        'exp_head_id' => 'required|integer',
        'name' => 'required|min_length[2]|max_length[50]',
        'date' => 'required|valid_date',
        'amount' => 'required|decimal|greater_than[0]',
        'invoice_no' => 'permit_empty|max_length[200]'
    ];

    protected $validationMessages = [
        'exp_head_id' => [
            'required' => 'Expense category is required',
            'integer' => 'Invalid expense category'
        ],
        'name' => [
            'required' => 'Expense name is required',
            'min_length' => 'Expense name must be at least 2 characters long',
            'max_length' => 'Expense name cannot exceed 50 characters'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'decimal' => 'Amount must be a valid number',
            'greater_than' => 'Amount must be greater than 0'
        ]
    ];

    protected $searchableColumns = ['name', 'invoice_no', 'note'];
    protected $orderableColumns = ['id', 'name', 'invoice_no', 'date', 'amount', 'created_at'];

    /**
     * Get expenses with category details
     */
    public function getExpensesWithCategory($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('expenses.*, expense_head.exp_category')
                ->join('expense_head', 'expenses.exp_head_id = expense_head.id')
                ->where('expenses.is_deleted', 'no');
        
        // Apply filters
        if (!empty($filters['exp_head_id'])) {
            $builder->where('expenses.exp_head_id', $filters['exp_head_id']);
        }
        
        if (!empty($filters['date_from'])) {
            $builder->where('expenses.date >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('expenses.date <=', $filters['date_to']);
        }
        
        if (!empty($filters['month'])) {
            $builder->where('MONTH(expenses.date)', $filters['month']);
        }
        
        if (!empty($filters['year'])) {
            $builder->where('YEAR(expenses.date)', $filters['year']);
        }
        
        return $builder->orderBy('expenses.date', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get monthly expense summary
     */
    public function getMonthlyExpenses($year = null)
    {
        $year = $year ?: date('Y');
        
        return $this->db->table($this->table)
                       ->select('MONTH(date) as month, SUM(amount) as total_amount, COUNT(*) as count')
                       ->where('YEAR(date)', $year)
                       ->where('is_deleted', 'no')
                       ->groupBy('MONTH(date)')
                       ->orderBy('month', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get expenses by category
     */
    public function getExpensesByCategory($year = null)
    {
        $year = $year ?: date('Y');
        
        return $this->db->table($this->table)
                       ->select('expense_head.exp_category, SUM(expenses.amount) as total_amount, COUNT(expenses.id) as count')
                       ->join('expense_head', 'expenses.exp_head_id = expense_head.id')
                       ->where('YEAR(expenses.date)', $year)
                       ->where('expenses.is_deleted', 'no')
                       ->groupBy('expense_head.id')
                       ->orderBy('total_amount', 'DESC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get expense statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total expenses
        $stats['total'] = $this->where('is_deleted', 'no')->countAllResults();
        
        // Total amount this year
        $stats['total_amount_year'] = $this->selectSum('amount')
                                          ->where('YEAR(date)', date('Y'))
                                          ->where('is_deleted', 'no')
                                          ->get()
                                          ->getRow()
                                          ->amount ?? 0;
        
        // Total amount this month
        $stats['total_amount_month'] = $this->selectSum('amount')
                                           ->where('MONTH(date)', date('m'))
                                           ->where('YEAR(date)', date('Y'))
                                           ->where('is_deleted', 'no')
                                           ->get()
                                           ->getRow()
                                           ->amount ?? 0;
        
        // Average expense amount
        $stats['average_amount'] = $this->selectAvg('amount')
                                       ->where('is_deleted', 'no')
                                       ->get()
                                       ->getRow()
                                       ->amount ?? 0;
        
        return $stats;
    }

    /**
     * Search expenses
     */
    public function searchExpenses($searchTerm, $filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('expenses.*, expense_head.exp_category')
                ->join('expense_head', 'expenses.exp_head_id = expense_head.id')
                ->where('expenses.is_deleted', 'no');
        
        if (!empty($searchTerm)) {
            $builder->groupStart()
                    ->like('expenses.name', $searchTerm)
                    ->orLike('expenses.invoice_no', $searchTerm)
                    ->orLike('expenses.note', $searchTerm)
                    ->orLike('expense_head.exp_category', $searchTerm)
                    ->groupEnd();
        }
        
        // Apply additional filters
        if (!empty($filters['exp_head_id'])) {
            $builder->where('expenses.exp_head_id', $filters['exp_head_id']);
        }
        
        if (!empty($filters['date_from'])) {
            $builder->where('expenses.date >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('expenses.date <=', $filters['date_to']);
        }
        
        return $builder->orderBy('expenses.date', 'DESC')->get()->getResultArray();
    }

    /**
     * Soft delete expense
     */
    public function softDelete($id)
    {
        return $this->update($id, ['is_deleted' => 'yes']);
    }

    /**
     * Restore soft deleted expense
     */
    public function restore($id)
    {
        return $this->update($id, ['is_deleted' => 'no']);
    }

    /**
     * Get deleted expenses
     */
    public function getDeleted()
    {
        return $this->db->table($this->table)
                       ->select('expenses.*, expense_head.exp_category')
                       ->join('expense_head', 'expenses.exp_head_id = expense_head.id')
                       ->where('expenses.is_deleted', 'yes')
                       ->orderBy('expenses.updated_at', 'DESC')
                       ->get()
                       ->getResultArray();
    }
}
