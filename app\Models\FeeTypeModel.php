<?php

namespace App\Models;

class FeeTypeModel extends BaseModel
{
    protected $table = 'feetype';
    protected $primaryKey = 'id';
    protected $allowedFields = ['is_system', 'feecategory_id', 'type', 'code', 'is_active', 'description'];

    protected $validationRules = [
        'type' => 'required|min_length[2]|max_length[50]',
        'code' => 'required|min_length[2]|max_length[100]|is_unique[feetype.code,id,{id}]'
    ];

    protected $validationMessages = [
        'type' => [
            'required' => 'Fee type is required',
            'min_length' => 'Fee type must be at least 2 characters long',
            'max_length' => 'Fee type cannot exceed 50 characters'
        ],
        'code' => [
            'required' => 'Fee code is required',
            'min_length' => 'Fee code must be at least 2 characters long',
            'max_length' => 'Fee code cannot exceed 100 characters',
            'is_unique' => 'Fee code already exists'
        ]
    ];

    protected $searchableColumns = ['type', 'code', 'description'];
    protected $orderableColumns = ['id', 'type', 'code', 'is_active', 'created_at'];

    /**
     * Get fee types for dropdown
     */
    public function getForDropdown()
    {
        $feeTypes = $this->where('is_active', 'yes')
                        ->orderBy('type', 'ASC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($feeTypes as $feeType) {
            $dropdown[$feeType['id']] = $feeType['type'] . ' (' . $feeType['code'] . ')';
        }
        
        return $dropdown;
    }

    /**
     * Get non-system fee types
     */
    public function getNonSystemTypes()
    {
        return $this->where('is_system', 0)
                   ->where('is_active', 'yes')
                   ->orderBy('type', 'ASC')
                   ->findAll();
    }

    /**
     * Get fee type statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total fee types
        $stats['total'] = $this->countAllResults();
        
        // Active fee types
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // System fee types
        $stats['system'] = $this->where('is_system', 1)->countAllResults();
        
        return $stats;
    }
}
