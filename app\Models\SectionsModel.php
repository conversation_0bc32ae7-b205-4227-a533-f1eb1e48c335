<?php

namespace App\Models;

class SectionsModel extends BaseModel
{
    protected $table = 'sections';
    protected $primaryKey = 'id';
    protected $allowedFields = ['section', 'is_active'];

    protected $validationRules = [
        'section' => 'required|min_length[1]|max_length[60]|is_unique[sections.section,id,{id}]'
    ];

    protected $validationMessages = [
        'section' => [
            'required' => 'Section name is required',
            'min_length' => 'Section name must be at least 1 character long',
            'max_length' => 'Section name cannot exceed 60 characters',
            'is_unique' => 'Section name already exists'
        ]
    ];

    protected $searchableColumns = ['section'];
    protected $orderableColumns = ['id', 'section', 'is_active', 'created_at'];

    /**
     * Get sections for dropdown
     */
    public function getForDropdown()
    {
        $sections = $this->where('is_active', 'yes')
                        ->orderBy('section', 'ASC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($sections as $section) {
            $dropdown[$section['id']] = $section['section'];
        }
        
        return $dropdown;
    }

    /**
     * Get sections by class
     */
    public function getByClass($classId)
    {
        return $this->db->table('class_sections')
                       ->select('sections.*')
                       ->join('sections', 'class_sections.section_id = sections.id')
                       ->where('class_sections.class_id', $classId)
                       ->where('class_sections.is_active', 'yes')
                       ->where('sections.is_active', 'yes')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get section statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total sections
        $stats['total'] = $this->countAllResults();
        
        // Active sections
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        return $stats;
    }
}
