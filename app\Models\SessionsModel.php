<?php

namespace App\Models;

class SessionsModel extends BaseModel
{
    protected $table = 'sessions';
    protected $primaryKey = 'id';
    protected $allowedFields = ['session', 'is_active'];

    protected $validationRules = [
        'session' => 'required|min_length[4]|max_length[100]|is_unique[sessions.session,id,{id}]'
    ];

    protected $validationMessages = [
        'session' => [
            'required' => 'Session is required',
            'min_length' => 'Session must be at least 4 characters long',
            'max_length' => 'Session cannot exceed 100 characters',
            'is_unique' => 'Session already exists'
        ]
    ];

    protected $searchableColumns = ['session'];
    protected $orderableColumns = ['id', 'session', 'is_active', 'created_at'];

    /**
     * Get current active session
     */
    public function getCurrentSession()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('created_at', 'DESC')
                   ->first();
    }

    /**
     * Get sessions for dropdown
     */
    public function getForDropdown()
    {
        $sessions = $this->where('is_active', 'yes')
                        ->orderBy('session', 'DESC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($sessions as $session) {
            $dropdown[$session['id']] = $session['session'];
        }
        
        return $dropdown;
    }

    /**
     * Set active session
     */
    public function setActiveSession($sessionId)
    {
        // First, deactivate all sessions
        $this->set('is_active', 'no')->update();
        
        // Then activate the selected session
        return $this->update($sessionId, ['is_active' => 'yes']);
    }

    /**
     * Get session statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total sessions
        $stats['total'] = $this->countAllResults();
        
        // Active sessions
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        return $stats;
    }
}
