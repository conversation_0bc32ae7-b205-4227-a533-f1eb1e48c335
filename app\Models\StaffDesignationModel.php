<?php

namespace App\Models;

class StaffDesignationModel extends BaseModel
{
    protected $table = 'staff_designation';
    protected $primaryKey = 'id';
    protected $allowedFields = ['designation', 'is_active'];

    protected $validationRules = [
        'designation' => 'required|min_length[2]|max_length[200]|is_unique[staff_designation.designation,id,{id}]'
    ];

    protected $validationMessages = [
        'designation' => [
            'required' => 'Designation is required',
            'min_length' => 'Designation must be at least 2 characters long',
            'max_length' => 'Designation cannot exceed 200 characters',
            'is_unique' => 'Designation already exists'
        ]
    ];

    protected $searchableColumns = ['designation'];
    protected $orderableColumns = ['id', 'designation', 'is_active', 'created_at'];

    /**
     * Get designations for dropdown
     */
    public function getForDropdown()
    {
        $designations = $this->where('is_active', 'yes')
                            ->orderBy('designation', 'ASC')
                            ->findAll();
        
        $dropdown = [];
        foreach ($designations as $designation) {
            $dropdown[$designation['id']] = $designation['designation'];
        }
        
        return $dropdown;
    }

    /**
     * Get designation statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total designations
        $stats['total'] = $this->countAllResults();
        
        // Active designations
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Designations with staff
        $stats['with_staff'] = $this->db->table($this->table)
                                       ->join('staff', 'staff_designation.id = staff.designation')
                                       ->distinct()
                                       ->countAllResults();
        
        return $stats;
    }
}
