<?php

namespace App\Models;

class StudentApplyLeaveModel extends BaseModel
{
    protected $table = 'student_applyleave';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'from_date', 'to_date', 'apply_date', 'status',
        'docs', 'reason', 'approve_by', 'approve_date', 'request_type'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'from_date' => 'required|valid_date',
        'to_date' => 'required|valid_date',
        'apply_date' => 'required|valid_date',
        'reason' => 'required|min_length[10]|max_length[500]',
        'status' => 'permit_empty|in_list[0,1,2]', // 0=Pending, 1=Approved, 2=Rejected
        'request_type' => 'permit_empty|in_list[0,1]' // 0=Student, 1=Staff
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'from_date' => [
            'required' => 'From date is required',
            'valid_date' => 'Please enter a valid from date'
        ],
        'to_date' => [
            'required' => 'To date is required',
            'valid_date' => 'Please enter a valid to date'
        ],
        'apply_date' => [
            'required' => 'Apply date is required',
            'valid_date' => 'Please enter a valid apply date'
        ],
        'reason' => [
            'required' => 'Reason is required',
            'min_length' => 'Reason must be at least 10 characters long',
            'max_length' => 'Reason cannot exceed 500 characters'
        ]
    ];

    protected $searchableColumns = ['reason'];
    protected $orderableColumns = ['id', 'from_date', 'to_date', 'apply_date', 'status', 'created_at'];

    /**
     * Get leave applications with student details
     */
    public function getLeaveApplicationsWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_applyleave.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, sessions.session,
                         staff.name as approved_by_name, staff.surname as approved_by_surname')
                ->join('student_session', 'student_applyleave.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id')
                ->join('staff', 'student_applyleave.approve_by = staff.id', 'left');

        // Apply filters
        if (!empty($filters['status'])) {
            $builder->where('student_applyleave.status', $filters['status']);
        }

        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['from_date'])) {
            $builder->where('student_applyleave.from_date >=', $filters['from_date']);
        }

        if (!empty($filters['to_date'])) {
            $builder->where('student_applyleave.to_date <=', $filters['to_date']);
        }

        return $builder->orderBy('student_applyleave.created_at', 'DESC');
    }

    /**
     * Approve leave application
     */
    public function approveLeave($id, $approvedBy)
    {
        $data = [
            'status' => 1, // Approved
            'approve_by' => $approvedBy,
            'approve_date' => date('Y-m-d')
        ];

        $result = $this->update($id, $data);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Leave application approved successfully',
                'data' => $this->find($id)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to approve leave application',
                'errors' => $this->errors()
            ];
        }
    }

    /**
     * Reject leave application
     */
    public function rejectLeave($id, $approvedBy)
    {
        $data = [
            'status' => 2, // Rejected
            'approve_by' => $approvedBy,
            'approve_date' => date('Y-m-d')
        ];

        $result = $this->update($id, $data);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Leave application rejected successfully',
                'data' => $this->find($id)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to reject leave application',
                'errors' => $this->errors()
            ];
        }
    }

    /**
     * Get leave statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total applications
        $stats['total'] = $this->countAllResults();
        
        // Pending applications
        $stats['pending'] = $this->where('status', 0)->countAllResults();
        
        // Approved applications
        $stats['approved'] = $this->where('status', 1)->countAllResults();
        
        // Rejected applications
        $stats['rejected'] = $this->where('status', 2)->countAllResults();
        
        // This month applications
        $stats['this_month'] = $this->where('MONTH(apply_date)', date('m'))
                                   ->where('YEAR(apply_date)', date('Y'))
                                   ->countAllResults();
        
        return $stats;
    }

    /**
     * Get student leave history
     */
    public function getStudentLeaveHistory($studentSessionId)
    {
        return $this->where('student_session_id', $studentSessionId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Check for overlapping leave dates
     */
    public function checkOverlappingLeave($studentSessionId, $fromDate, $toDate, $excludeId = null)
    {
        $builder = $this->where('student_session_id', $studentSessionId)
                       ->where('status', 1) // Only approved leaves
                       ->groupStart()
                           ->where('from_date <=', $toDate)
                           ->where('to_date >=', $fromDate)
                       ->groupEnd();

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Set default values
        $data['status'] = $data['status'] ?? 0; // Pending by default
        $data['request_type'] = $data['request_type'] ?? 0; // Student request by default
        $data['apply_date'] = $data['apply_date'] ?? date('Y-m-d');

        // Validate date range
        if (!empty($data['from_date']) && !empty($data['to_date'])) {
            if (strtotime($data['from_date']) > strtotime($data['to_date'])) {
                throw new \Exception('From date cannot be greater than to date');
            }
        }

        return $data;
    }
}
