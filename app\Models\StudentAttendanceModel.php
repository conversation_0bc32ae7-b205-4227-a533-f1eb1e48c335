<?php

namespace App\Models;

class StudentAttendanceModel extends BaseModel
{
    protected $table = 'student_attendences';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'biometric_attendence', 'qrcode_attendance', 'date',
        'attendence_type_id', 'remark', 'biometric_device_data', 'user_agent'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'date' => 'required|valid_date',
        'attendence_type_id' => 'required|integer'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'attendence_type_id' => [
            'required' => 'Attendance type is required',
            'integer' => 'Invalid attendance type'
        ]
    ];

    protected $searchableColumns = ['date', 'remark'];
    protected $orderableColumns = ['id', 'date', 'attendence_type_id', 'created_at'];

    /**
     * Get attendance with student details
     */
    public function getAttendanceWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_attendences.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, attendence_type.type as attendance_type')
                ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['date'])) {
            $builder->where('student_attendences.date', $filters['date']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('student_attendences.date >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('student_attendences.date <=', $filters['date_to']);
        }

        if (!empty($filters['attendance_type_id'])) {
            $builder->where('student_attendences.attendence_type_id', $filters['attendance_type_id']);
        }

        return $builder->orderBy('student_attendences.date', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Mark attendance for multiple students
     */
    public function markBulkAttendance($attendanceData, $date)
    {
        $this->db->transStart();

        try {
            $markedCount = 0;

            foreach ($attendanceData as $studentSessionId => $attendanceTypeId) {
                // Check if attendance already exists for this date
                $existing = $this->where('student_session_id', $studentSessionId)
                               ->where('date', $date)
                               ->first();

                $data = [
                    'student_session_id' => $studentSessionId,
                    'date' => $date,
                    'attendence_type_id' => $attendanceTypeId,
                    'biometric_attendence' => 0,
                    'qrcode_attendance' => 0,
                    'remark' => '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];

                if ($existing) {
                    // Update existing attendance
                    $this->update($existing['id'], $data);
                } else {
                    // Insert new attendance
                    $this->insert($data);
                }

                $markedCount++;
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to mark attendance',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => "Attendance marked for $markedCount students",
                'data' => ['marked_count' => $markedCount]
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => 'Error occurred while marking attendance: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get attendance summary for a student
     */
    public function getStudentAttendanceSummary($studentId, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        return $this->db->table($this->table)
                       ->select('attendence_type.type, COUNT(*) as count')
                       ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                       ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                       ->where('student_session.student_id', $studentId)
                       ->where('MONTH(student_attendences.date)', $month)
                       ->where('YEAR(student_attendences.date)', $year)
                       ->groupBy('attendence_type.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get class attendance summary
     */
    public function getClassAttendanceSummary($classId, $sectionId, $date)
    {
        return $this->db->table($this->table)
                       ->select('attendence_type.type, COUNT(*) as count')
                       ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                       ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                       ->where('student_session.class_id', $classId)
                       ->where('student_session.section_id', $sectionId)
                       ->where('student_attendences.date', $date)
                       ->groupBy('attendence_type.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get monthly attendance report
     */
    public function getMonthlyAttendanceReport($classId = null, $sectionId = null, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        $builder = $this->db->table($this->table);
        $builder->select('DAY(student_attendences.date) as day, 
                         attendence_type.type, 
                         COUNT(*) as count')
                ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                ->where('MONTH(student_attendences.date)', $month)
                ->where('YEAR(student_attendences.date)', $year);

        if ($classId) {
            $builder->where('student_session.class_id', $classId);
        }

        if ($sectionId) {
            $builder->where('student_session.section_id', $sectionId);
        }

        return $builder->groupBy('DAY(student_attendences.date), attendence_type.id')
                      ->orderBy('day', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get attendance statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total attendance records
        $stats['total'] = $this->countAllResults();
        
        // Today's attendance
        $stats['today'] = $this->where('date', date('Y-m-d'))->countAllResults();
        
        // This month's attendance
        $stats['this_month'] = $this->where('MONTH(date)', date('m'))
                                   ->where('YEAR(date)', date('Y'))
                                   ->countAllResults();
        
        // Attendance by type (today)
        $stats['by_type_today'] = $this->db->table($this->table)
                                          ->select('attendence_type.type, COUNT(*) as count')
                                          ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                                          ->where('student_attendences.date', date('Y-m-d'))
                                          ->groupBy('attendence_type.id')
                                          ->get()
                                          ->getResultArray();
        
        return $stats;
    }

    /**
     * Get students with no attendance for a date
     */
    public function getAbsentStudents($classId, $sectionId, $date, $sessionId = null)
    {
        $builder = $this->db->table('student_session');
        $builder->select('student_session.*, students.firstname, students.lastname, students.admission_no')
                ->join('students', 'student_session.student_id = students.id')
                ->where('student_session.class_id', $classId)
                ->where('student_session.section_id', $sectionId)
                ->where('student_session.is_active', 'yes');

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        // Exclude students who have attendance for this date
        $builder->whereNotIn('student_session.id', function($subquery) use ($date) {
            return $subquery->select('student_session_id')
                           ->from('student_attendences')
                           ->where('date', $date);
        });

        return $builder->get()->getResultArray();
    }

    /**
     * Get attendance types
     */
    public function getAttendanceTypes()
    {
        try {
            $types = $this->db->table('attendence_type')
                             ->where('is_active', 'yes')
                             ->get()
                             ->getResultArray();
            
            $dropdown = [];
            foreach ($types as $type) {
                $dropdown[$type['id']] = $type['type'];
            }
            
            return $dropdown;
        } catch (\Exception $e) {
            return [
                1 => 'Present',
                2 => 'Absent',
                3 => 'Late',
                4 => 'Half Day'
            ];
        }
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }
}
