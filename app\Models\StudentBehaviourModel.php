<?php

namespace App\Models;

class StudentBehaviourModel extends BaseModel
{
    protected $table = 'student_behaviour';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'point', 'description', 'title'
    ];

    protected $validationRules = [
        'title' => 'required|min_length[3]|max_length[255]',
        'description' => 'required|min_length[10]|max_length[1000]',
        'point' => 'required|integer|greater_than_equal_to[-100]|less_than_equal_to[100]'
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'Title is required',
            'min_length' => 'Title must be at least 3 characters long',
            'max_length' => 'Title cannot exceed 255 characters'
        ],
        'description' => [
            'required' => 'Description is required',
            'min_length' => 'Description must be at least 10 characters long',
            'max_length' => 'Description cannot exceed 1000 characters'
        ],
        'point' => [
            'required' => 'Point is required',
            'integer' => 'Point must be a valid integer',
            'greater_than_equal_to' => 'Point must be between -100 and 100',
            'less_than_equal_to' => 'Point must be between -100 and 100'
        ]
    ];

    protected $searchableColumns = ['title', 'description'];
    protected $orderableColumns = ['id', 'title', 'point', 'created_at'];

    /**
     * Get behaviour records for dropdown
     */
    public function getForDropdown()
    {
        $behaviours = $this->orderBy('title', 'ASC')->findAll();
        
        $dropdown = [];
        foreach ($behaviours as $behaviour) {
            $label = $behaviour['title'] . ' (' . ($behaviour['point'] >= 0 ? '+' : '') . $behaviour['point'] . ' points)';
            $dropdown[$behaviour['id']] = $label;
        }
        
        return $dropdown;
    }

    /**
     * Get positive behaviours
     */
    public function getPositiveBehaviours()
    {
        return $this->where('point >', 0)
                   ->orderBy('point', 'DESC')
                   ->findAll();
    }

    /**
     * Get negative behaviours
     */
    public function getNegativeBehaviours()
    {
        return $this->where('point <', 0)
                   ->orderBy('point', 'ASC')
                   ->findAll();
    }

    /**
     * Get neutral behaviours
     */
    public function getNeutralBehaviours()
    {
        return $this->where('point', 0)
                   ->orderBy('title', 'ASC')
                   ->findAll();
    }

    /**
     * Get behaviour statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total behaviours
        $stats['total'] = $this->countAllResults();
        
        // Positive behaviours
        $stats['positive'] = $this->where('point >', 0)->countAllResults();
        
        // Negative behaviours
        $stats['negative'] = $this->where('point <', 0)->countAllResults();
        
        // Neutral behaviours
        $stats['neutral'] = $this->where('point', 0)->countAllResults();
        
        // Average points
        $result = $this->selectAvg('point')->first();
        $stats['average_points'] = round($result['point'] ?? 0, 2);
        
        // Highest positive points
        $highest = $this->selectMax('point')->first();
        $stats['highest_points'] = $highest['point'] ?? 0;
        
        // Lowest negative points
        $lowest = $this->selectMin('point')->first();
        $stats['lowest_points'] = $lowest['point'] ?? 0;
        
        return $stats;
    }

    /**
     * Get behaviour categories
     */
    public function getBehaviourCategories()
    {
        return [
            'positive' => $this->getPositiveBehaviours(),
            'negative' => $this->getNegativeBehaviours(),
            'neutral' => $this->getNeutralBehaviours()
        ];
    }

    /**
     * Search behaviours by point range
     */
    public function searchByPointRange($minPoint = null, $maxPoint = null)
    {
        $builder = $this;
        
        if ($minPoint !== null) {
            $builder = $builder->where('point >=', $minPoint);
        }
        
        if ($maxPoint !== null) {
            $builder = $builder->where('point <=', $maxPoint);
        }
        
        return $builder->orderBy('point', 'DESC')->findAll();
    }

    /**
     * Get most used behaviours (this would require a student_incidents table join)
     */
    public function getMostUsedBehaviours($limit = 10)
    {
        // This would typically join with student_incidents table to count usage
        // For now, return all behaviours ordered by creation date
        return $this->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Validate point range
     */
    public function validatePointRange($point)
    {
        return $point >= -100 && $point <= 100;
    }

    /**
     * Get behaviour impact level
     */
    public function getBehaviourImpact($point)
    {
        if ($point >= 50) {
            return ['level' => 'Excellent', 'color' => 'success', 'icon' => 'fas fa-star'];
        } elseif ($point >= 20) {
            return ['level' => 'Good', 'color' => 'primary', 'icon' => 'fas fa-thumbs-up'];
        } elseif ($point > 0) {
            return ['level' => 'Positive', 'color' => 'info', 'icon' => 'fas fa-plus'];
        } elseif ($point == 0) {
            return ['level' => 'Neutral', 'color' => 'secondary', 'icon' => 'fas fa-minus'];
        } elseif ($point >= -20) {
            return ['level' => 'Minor Issue', 'color' => 'warning', 'icon' => 'fas fa-exclamation'];
        } elseif ($point >= -50) {
            return ['level' => 'Serious Issue', 'color' => 'danger', 'icon' => 'fas fa-exclamation-triangle'];
        } else {
            return ['level' => 'Critical Issue', 'color' => 'danger', 'icon' => 'fas fa-ban'];
        }
    }
}
