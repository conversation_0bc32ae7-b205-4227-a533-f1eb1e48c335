<?php

namespace App\Models;

class StudentEditFieldsModel extends BaseModel
{
    protected $table = 'student_edit_fields';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'status'
    ];

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]|is_unique[student_edit_fields.name,id,{id}]',
        'status' => 'required|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Field name is required',
            'min_length' => 'Field name must be at least 3 characters long',
            'max_length' => 'Field name cannot exceed 100 characters',
            'is_unique' => 'Field name already exists'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Invalid status value'
        ]
    ];

    protected $searchableColumns = ['name'];
    protected $orderableColumns = ['id', 'name', 'status', 'created_at'];

    /**
     * Get field types
     */
    public function getFieldTypes()
    {
        return [
            'text' => 'Text Field',
            'email' => 'Email Field',
            'number' => 'Number Field',
            'date' => 'Date Field',
            'textarea' => 'Textarea Field',
            'select' => 'Select Dropdown',
            'checkbox' => 'Checkbox',
            'radio' => 'Radio Button',
            'file' => 'File Upload'
        ];
    }

    /**
     * Get status options
     */
    public function getStatusOptions()
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive'
        ];
    }

    /**
     * Get active fields
     */
    public function getActiveFields()
    {
        return $this->where('status', 'active')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get inactive fields
     */
    public function getInactiveFields()
    {
        return $this->where('status', 'inactive')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Toggle field status
     */
    public function toggleFieldStatus($id)
    {
        $field = $this->find($id);
        if (!$field) {
            return [
                'success' => false,
                'message' => 'Field not found',
                'errors' => []
            ];
        }

        $newStatus = $field['status'] === 'active' ? 'inactive' : 'active';
        
        $result = $this->update($id, ['status' => $newStatus]);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Field status updated successfully',
                'data' => $this->find($id)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to update field status',
                'errors' => $this->errors()
            ];
        }
    }

    /**
     * Get field statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total fields
        $stats['total'] = $this->countAllResults();
        
        // Active fields
        $stats['active'] = $this->where('status', 'active')->countAllResults();
        
        // Inactive fields
        $stats['inactive'] = $this->where('status', 'inactive')->countAllResults();
        
        // Recently added (this month)
        $stats['this_month'] = $this->where('MONTH(created_at)', date('m'))
                                   ->where('YEAR(created_at)', date('Y'))
                                   ->countAllResults();
        
        return $stats;
    }

    /**
     * Get fields for dropdown
     */
    public function getForDropdown($activeOnly = true)
    {
        $builder = $this;
        
        if ($activeOnly) {
            $builder = $builder->where('status', 'active');
        }
        
        $fields = $builder->orderBy('name', 'ASC')->findAll();
        
        $dropdown = [];
        foreach ($fields as $field) {
            $dropdown[$field['id']] = $field['name'];
        }
        
        return $dropdown;
    }

    /**
     * Get predefined student fields
     */
    public function getPredefinedFields()
    {
        return [
            'firstname' => 'First Name',
            'lastname' => 'Last Name',
            'email' => 'Email Address',
            'phone' => 'Phone Number',
            'address' => 'Address',
            'date_of_birth' => 'Date of Birth',
            'gender' => 'Gender',
            'blood_group' => 'Blood Group',
            'religion' => 'Religion',
            'caste' => 'Caste',
            'category_id' => 'Category',
            'admission_no' => 'Admission Number',
            'roll_no' => 'Roll Number',
            'admission_date' => 'Admission Date',
            'father_name' => 'Father Name',
            'father_phone' => 'Father Phone',
            'father_occupation' => 'Father Occupation',
            'mother_name' => 'Mother Name',
            'mother_phone' => 'Mother Phone',
            'mother_occupation' => 'Mother Occupation',
            'guardian_name' => 'Guardian Name',
            'guardian_phone' => 'Guardian Phone',
            'guardian_email' => 'Guardian Email',
            'guardian_address' => 'Guardian Address',
            'guardian_relation' => 'Guardian Relation',
            'guardian_occupation' => 'Guardian Occupation',
            'previous_school' => 'Previous School',
            'height' => 'Height',
            'weight' => 'Weight',
            'medical_history' => 'Medical History',
            'note' => 'Note',
            'is_active' => 'Active Status'
        ];
    }

    /**
     * Initialize default fields
     */
    public function initializeDefaultFields()
    {
        $defaultFields = [
            'firstname',
            'lastname', 
            'email',
            'phone',
            'address',
            'date_of_birth',
            'gender',
            'admission_no',
            'father_name',
            'mother_name'
        ];

        $predefinedFields = $this->getPredefinedFields();
        
        foreach ($defaultFields as $fieldName) {
            $existing = $this->where('name', $fieldName)->first();
            if (!$existing) {
                $this->insert([
                    'name' => $fieldName,
                    'status' => 'active'
                ]);
            }
        }
    }

    /**
     * Validate field name
     */
    public function validateFieldName($name, $excludeId = null)
    {
        $builder = $this->where('name', $name);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() === 0;
    }

    /**
     * Get field usage statistics
     */
    public function getFieldUsageStats()
    {
        // This would typically check how many students have data in each field
        // For now, return basic statistics
        $fields = $this->findAll();
        $stats = [];
        
        foreach ($fields as $field) {
            $stats[] = [
                'field_name' => $field['name'],
                'status' => $field['status'],
                'usage_count' => 0, // Would be calculated from student data
                'last_used' => null // Would be calculated from student data
            ];
        }
        
        return $stats;
    }

    /**
     * Export field configuration
     */
    public function exportConfiguration()
    {
        $fields = $this->orderBy('name', 'ASC')->findAll();
        
        $export = [
            'export_date' => date('Y-m-d H:i:s'),
            'total_fields' => count($fields),
            'fields' => $fields
        ];
        
        return $export;
    }

    /**
     * Import field configuration
     */
    public function importConfiguration($configData)
    {
        if (!isset($configData['fields']) || !is_array($configData['fields'])) {
            return [
                'success' => false,
                'message' => 'Invalid configuration data',
                'errors' => []
            ];
        }

        $imported = 0;
        $errors = [];

        foreach ($configData['fields'] as $fieldData) {
            if (!isset($fieldData['name']) || !isset($fieldData['status'])) {
                $errors[] = 'Missing required field data';
                continue;
            }

            $existing = $this->where('name', $fieldData['name'])->first();
            if ($existing) {
                // Update existing field
                $this->update($existing['id'], [
                    'status' => $fieldData['status']
                ]);
            } else {
                // Create new field
                $this->insert([
                    'name' => $fieldData['name'],
                    'status' => $fieldData['status']
                ]);
            }
            
            $imported++;
        }

        return [
            'success' => true,
            'message' => "Successfully imported {$imported} fields",
            'data' => [
                'imported_count' => $imported,
                'error_count' => count($errors),
                'errors' => $errors
            ]
        ];
    }
}
