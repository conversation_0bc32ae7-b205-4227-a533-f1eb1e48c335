<?php

namespace App\Models;

class StudentFeesModel extends BaseModel
{
    protected $table = 'student_fees_deposite';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'fee_session_group_id', 'student_fees_master_id', 'amount_detail',
        'amount_fine', 'description', 'payment_mode', 'date', 'payment_mode_message',
        'is_active', 'received_by', 'inv_no'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'amount_detail' => 'required|decimal|greater_than_equal_to[0]',
        'date' => 'required|valid_date'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'amount_detail' => [
            'required' => 'Amount is required',
            'decimal' => 'Amount must be a valid number',
            'greater_than_equal_to' => 'Amount must be greater than or equal to 0'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ]
    ];

    protected $searchableColumns = ['amount_detail', 'description', 'inv_no'];
    protected $orderableColumns = ['id', 'amount_detail', 'date', 'is_active', 'created_at'];

    /**
     * Get student fees with details
     */
    public function getStudentFeesWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_fees_master.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, sessions.session, fee_session_groups.name as fee_group_name')
                ->join('student_session', 'student_fees_master.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id')
                ->join('fee_session_groups', 'student_fees_master.fee_session_group_id = fee_session_groups.id');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['session_id'])) {
            $builder->where('student_session.session_id', $filters['session_id']);
        }

        if (!empty($filters['student_id'])) {
            $builder->where('student_session.student_id', $filters['student_id']);
        }

        if (!empty($filters['fee_group_id'])) {
            $builder->where('student_fees_master.fee_session_group_id', $filters['fee_group_id']);
        }

        return $builder->orderBy('student_fees_master.created_at', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get student fee summary
     */
    public function getStudentFeeSummary($studentId, $sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_fees_master.*, fee_session_groups.name as fee_group_name,
                         COALESCE(SUM(student_fees_deposite.amount_detail), 0) as paid_amount')
                ->join('student_session', 'student_fees_master.student_session_id = student_session.id')
                ->join('fee_session_groups', 'student_fees_master.fee_session_group_id = fee_session_groups.id')
                ->join('student_fees_deposite', 'student_fees_master.id = student_fees_deposite.student_fees_master_id', 'left')
                ->where('student_session.student_id', $studentId);

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        return $builder->groupBy('student_fees_master.id')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Assign fees to student
     */
    public function assignFeesToStudent($studentSessionId, $feeGroupId, $amount)
    {
        // Check if fees already assigned
        $existing = $this->where('student_session_id', $studentSessionId)
                        ->where('fee_session_group_id', $feeGroupId)
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Fees already assigned to this student for this group',
                'data' => null
            ];
        }

        $data = [
            'student_session_id' => $studentSessionId,
            'fee_session_group_id' => $feeGroupId,
            'amount' => $amount,
            'is_system' => 0,
            'is_active' => 'yes'
        ];

        return $this->createRecord($data);
    }

    /**
     * Bulk assign fees to class
     */
    public function bulkAssignFeesToClass($classId, $sectionId, $sessionId, $feeGroupId, $amount)
    {
        // Get all students in the class
        $studentSessions = $this->db->table('student_session')
                                   ->where('class_id', $classId)
                                   ->where('section_id', $sectionId)
                                   ->where('session_id', $sessionId)
                                   ->where('is_active', 'yes')
                                   ->get()
                                   ->getResultArray();

        if (empty($studentSessions)) {
            return [
                'success' => false,
                'message' => 'No students found in the specified class',
                'data' => null
            ];
        }

        $this->db->transStart();

        try {
            $assignedCount = 0;

            foreach ($studentSessions as $session) {
                // Check if fees already assigned
                $existing = $this->where('student_session_id', $session['id'])
                               ->where('fee_session_group_id', $feeGroupId)
                               ->first();

                if (!$existing) {
                    $data = [
                        'student_session_id' => $session['id'],
                        'fee_session_group_id' => $feeGroupId,
                        'amount' => $amount,
                        'is_system' => 0,
                        'is_active' => 'yes'
                    ];

                    $this->insert($data);
                    $assignedCount++;
                }
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to assign fees',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => "Fees assigned to $assignedCount students",
                'data' => ['assigned_count' => $assignedCount]
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => 'Error occurred while assigning fees: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get fee collection summary
     */
    public function getFeeCollectionSummary($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('SUM(student_fees_master.amount) as total_fees,
                         COALESCE(SUM(student_fees_deposite.amount_detail), 0) as collected_fees,
                         (SUM(student_fees_master.amount) - COALESCE(SUM(student_fees_deposite.amount_detail), 0)) as pending_fees')
                ->join('student_session', 'student_fees_master.student_session_id = student_session.id')
                ->join('student_fees_deposite', 'student_fees_master.id = student_fees_deposite.student_fees_master_id', 'left')
                ->where('student_fees_master.is_active', 'yes');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['session_id'])) {
            $builder->where('student_session.session_id', $filters['session_id']);
        }

        return $builder->get()->getRowArray();
    }

    /**
     * Get monthly fee collection
     */
    public function getMonthlyFeeCollection($year = null)
    {
        $year = $year ?: date('Y');

        return $this->db->table('student_fees_deposite')
                       ->select('MONTH(created_at) as month, SUM(amount_detail) as total_collected')
                       ->where('YEAR(created_at)', $year)
                       ->where('is_active', 'yes')
                       ->groupBy('MONTH(created_at)')
                       ->orderBy('month', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get fee statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total fee records
        $stats['total'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Total fees amount
        $totalFeesResult = $this->selectSum('amount')
                               ->where('is_active', 'yes')
                               ->get()
                               ->getRow();
        $stats['total_fees_amount'] = $totalFeesResult ? ($totalFeesResult->amount ?? 0) : 0;
        
        // Total collected amount
        $collectedResult = $this->db->table('student_fees_deposite')
                                   ->selectSum('amount_detail')
                                   ->where('is_active', 'yes')
                                   ->get()
                                   ->getRow();
        $stats['total_collected'] = $collectedResult ? ($collectedResult->amount_detail ?? 0) : 0;
        
        // Pending amount
        $stats['pending_amount'] = $stats['total_fees_amount'] - $stats['total_collected'];
        
        // This month's collection
        $monthlyResult = $this->db->table('student_fees_deposite')
                                 ->selectSum('amount_detail')
                                 ->where('MONTH(created_at)', date('m'))
                                 ->where('YEAR(created_at)', date('Y'))
                                 ->where('is_active', 'yes')
                                 ->get()
                                 ->getRow();
        $stats['monthly_collection'] = $monthlyResult ? ($monthlyResult->amount_detail ?? 0) : 0;
        
        return $stats;
    }

    /**
     * Get defaulters list
     */
    public function getDefaulters($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_fees_master.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, fee_session_groups.name as fee_group_name,
                         (student_fees_master.amount - COALESCE(SUM(student_fees_deposite.amount_detail), 0)) as pending_amount')
                ->join('student_session', 'student_fees_master.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('fee_session_groups', 'student_fees_master.fee_session_group_id = fee_session_groups.id')
                ->join('student_fees_deposite', 'student_fees_master.id = student_fees_deposite.student_fees_master_id', 'left')
                ->where('student_fees_master.is_active', 'yes')
                ->groupBy('student_fees_master.id')
                ->having('pending_amount > 0');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        return $builder->orderBy('pending_amount', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }
}
