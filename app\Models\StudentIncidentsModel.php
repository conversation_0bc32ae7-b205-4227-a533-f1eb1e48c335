<?php

namespace App\Models;

class StudentIncidentsModel extends BaseModel
{
    protected $table = 'student_incidents';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'incident_date', 'title', 'description', 'action_taken',
        'assigned_by', 'student_behaviour_id', 'point'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'incident_date' => 'required|valid_date',
        'title' => 'required|min_length[3]|max_length[200]',
        'description' => 'required|min_length[10]',
        'action_taken' => 'permit_empty|max_length[1000]',
        'assigned_by' => 'required|integer',
        'student_behaviour_id' => 'permit_empty|integer',
        'point' => 'permit_empty|integer|greater_than_equal_to[-100]|less_than_equal_to[100]'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'incident_date' => [
            'required' => 'Incident date is required',
            'valid_date' => 'Please enter a valid incident date'
        ],
        'title' => [
            'required' => 'Title is required',
            'min_length' => 'Title must be at least 3 characters long',
            'max_length' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'required' => 'Description is required',
            'min_length' => 'Description must be at least 10 characters long'
        ],
        'action_taken' => [
            'max_length' => 'Action taken cannot exceed 1000 characters'
        ],
        'assigned_by' => [
            'required' => 'Assigned by is required',
            'integer' => 'Invalid assigned by value'
        ],
        'student_behaviour_id' => [
            'integer' => 'Invalid behaviour selection'
        ],
        'point' => [
            'integer' => 'Point must be a valid integer',
            'greater_than_equal_to' => 'Point must be between -100 and 100',
            'less_than_equal_to' => 'Point must be between -100 and 100'
        ]
    ];

    protected $searchableColumns = ['title', 'description', 'action_taken'];
    protected $orderableColumns = ['id', 'incident_date', 'title', 'point', 'created_at'];

    /**
     * Get incidents with student details
     */
    public function getIncidentsWithStudentDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_incidents.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section,
                         staff.name as assigned_by_name, staff.surname as assigned_by_surname,
                         student_behaviour.title as behaviour_title, student_behaviour.point as behaviour_point')
                ->join('student_session', 'student_incidents.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('staff', 'student_incidents.assigned_by = staff.id', 'left')
                ->join('student_behaviour', 'student_incidents.student_behaviour_id = student_behaviour.id', 'left');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['from_date'])) {
            $builder->where('student_incidents.incident_date >=', $filters['from_date']);
        }

        if (!empty($filters['to_date'])) {
            $builder->where('student_incidents.incident_date <=', $filters['to_date']);
        }

        return $builder->orderBy('student_incidents.incident_date', 'DESC');
    }

    /**
     * Get incident statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total incidents
        $stats['total'] = $this->countAllResults();
        
        // This month incidents
        $stats['this_month'] = $this->where('MONTH(incident_date)', date('m'))
                                   ->where('YEAR(incident_date)', date('Y'))
                                   ->countAllResults();
        
        // This week incidents
        $stats['this_week'] = $this->where('incident_date >=', date('Y-m-d', strtotime('monday this week')))
                                  ->where('incident_date <=', date('Y-m-d', strtotime('sunday this week')))
                                  ->countAllResults();
        
        // Today's incidents
        $stats['today'] = $this->where('incident_date', date('Y-m-d'))->countAllResults();
        
        // Incidents with positive points
        $stats['positive_incidents'] = $this->where('point >', 0)->countAllResults();
        
        // Incidents with negative points
        $stats['negative_incidents'] = $this->where('point <', 0)->countAllResults();
        
        // Average points
        $result = $this->selectAvg('point')->first();
        $stats['average_points'] = round($result['point'] ?? 0, 2);
        
        // Students with incidents this month
        $stats['students_with_incidents'] = $this->db->table($this->table)
                                                    ->select('student_session_id')
                                                    ->distinct()
                                                    ->where('MONTH(incident_date)', date('m'))
                                                    ->where('YEAR(incident_date)', date('Y'))
                                                    ->countAllResults();
        
        return $stats;
    }

    /**
     * Get student incident history
     */
    public function getStudentIncidentHistory($studentSessionId, $limit = null)
    {
        $builder = $this->where('student_session_id', $studentSessionId)
                       ->orderBy('incident_date', 'DESC');
        
        if ($limit) {
            $builder->limit($limit);
        }
        
        return $builder->findAll();
    }

    /**
     * Get incidents by date range
     */
    public function getIncidentsByDateRange($fromDate, $toDate, $studentSessionId = null)
    {
        $builder = $this->where('incident_date >=', $fromDate)
                       ->where('incident_date <=', $toDate);
        
        if ($studentSessionId) {
            $builder->where('student_session_id', $studentSessionId);
        }
        
        return $builder->orderBy('incident_date', 'DESC')->findAll();
    }

    /**
     * Get recent incidents
     */
    public function getRecentIncidents($limit = 10)
    {
        return $this->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get incidents by behaviour type
     */
    public function getIncidentsByBehaviour($behaviourId)
    {
        return $this->where('student_behaviour_id', $behaviourId)
                   ->orderBy('incident_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get incident summary for student
     */
    public function getStudentIncidentSummary($studentSessionId)
    {
        $summary = [];
        
        // Total incidents
        $summary['total_incidents'] = $this->where('student_session_id', $studentSessionId)->countAllResults();
        
        // Incidents this month
        $summary['this_month'] = $this->where('student_session_id', $studentSessionId)
                                     ->where('MONTH(incident_date)', date('m'))
                                     ->where('YEAR(incident_date)', date('Y'))
                                     ->countAllResults();
        
        // Total points earned/lost
        $result = $this->selectSum('point')
                      ->where('student_session_id', $studentSessionId)
                      ->first();
        $summary['total_points'] = $result['point'] ?? 0;
        
        // Positive incidents
        $summary['positive_incidents'] = $this->where('student_session_id', $studentSessionId)
                                             ->where('point >', 0)
                                             ->countAllResults();
        
        // Negative incidents
        $summary['negative_incidents'] = $this->where('student_session_id', $studentSessionId)
                                             ->where('point <', 0)
                                             ->countAllResults();
        
        // Recent incidents
        $summary['recent_incidents'] = $this->getStudentIncidentHistory($studentSessionId, 5);
        
        // Incidents by month (current year)
        $summary['monthly_incidents'] = $this->db->table($this->table)
                                                ->select('MONTH(incident_date) as month, COUNT(*) as count')
                                                ->where('student_session_id', $studentSessionId)
                                                ->where('YEAR(incident_date)', date('Y'))
                                                ->groupBy('MONTH(incident_date)')
                                                ->orderBy('month', 'ASC')
                                                ->get()
                                                ->getResultArray();
        
        return $summary;
    }

    /**
     * Get incident types/categories
     */
    public function getIncidentTypes()
    {
        return [
            'Academic' => [
                'Late Assignment',
                'Missing Homework',
                'Cheating',
                'Disrupting Class',
                'Not Following Instructions'
            ],
            'Behavioral' => [
                'Fighting',
                'Bullying',
                'Disrespectful Behavior',
                'Inappropriate Language',
                'Vandalism'
            ],
            'Attendance' => [
                'Tardiness',
                'Unexcused Absence',
                'Skipping Class',
                'Leaving Without Permission'
            ],
            'Positive' => [
                'Academic Excellence',
                'Helping Others',
                'Leadership',
                'Good Citizenship',
                'Improvement'
            ]
        ];
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Set default values
        $data['incident_date'] = $data['incident_date'] ?? date('Y-m-d');
        $data['assigned_by'] = $data['assigned_by'] ?? 1; // TODO: Get from session/auth

        // If behaviour is selected, get its point value
        if (!empty($data['student_behaviour_id']) && empty($data['point'])) {
            $behaviourModel = new \App\Models\StudentBehaviourModel();
            $behaviour = $behaviourModel->find($data['student_behaviour_id']);
            if ($behaviour) {
                $data['point'] = $behaviour['point'];
            }
        }

        return $data;
    }
}
