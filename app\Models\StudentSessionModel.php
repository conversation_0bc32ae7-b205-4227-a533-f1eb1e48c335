<?php

namespace App\Models;

class StudentSessionModel extends BaseModel
{
    protected $table = 'student_session';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'session_id', 'student_id', 'class_id', 'section_id', 'hostel_room_id',
        'vehroute_id', 'route_pickup_point_id', 'transport_fees', 'fees_discount',
        'is_leave', 'is_active', 'is_alumni', 'default_login'
    ];

    protected $validationRules = [
        'session_id' => 'required|integer',
        'student_id' => 'required|integer',
        'class_id' => 'required|integer',
        'section_id' => 'required|integer',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'session_id' => [
            'required' => 'Session is required',
            'integer' => 'Invalid session'
        ],
        'student_id' => [
            'required' => 'Student is required',
            'integer' => 'Invalid student'
        ],
        'class_id' => [
            'required' => 'Class is required',
            'integer' => 'Invalid class'
        ],
        'section_id' => [
            'required' => 'Section is required',
            'integer' => 'Invalid section'
        ]
    ];

    protected $searchableColumns = ['student_id', 'class_id', 'section_id'];
    protected $orderableColumns = ['id', 'session_id', 'student_id', 'class_id', 'section_id', 'is_active', 'created_at'];

    /**
     * Get student session with details
     */
    public function getStudentSessionWithDetails($studentId = null, $sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_session.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, sessions.session')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id');

        if ($studentId) {
            $builder->where('student_session.student_id', $studentId);
        }

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        return $builder->orderBy('student_session.created_at', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get current session for student
     */
    public function getCurrentSession($studentId)
    {
        return $this->db->table($this->table)
                       ->select('student_session.*, classes.class, sections.section, sessions.session')
                       ->join('classes', 'student_session.class_id = classes.id')
                       ->join('sections', 'student_session.section_id = sections.id')
                       ->join('sessions', 'student_session.session_id = sessions.id')
                       ->where('student_session.student_id', $studentId)
                       ->where('student_session.is_active', 'yes')
                       ->orderBy('student_session.created_at', 'DESC')
                       ->get()
                       ->getRowArray();
    }

    /**
     * Get students by class and section
     */
    public function getStudentsByClassSection($classId, $sectionId, $sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_session.*, students.firstname, students.lastname, students.admission_no, students.roll_no')
                ->join('students', 'student_session.student_id = students.id')
                ->where('student_session.class_id', $classId)
                ->where('student_session.section_id', $sectionId)
                ->where('student_session.is_active', 'yes');

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        return $builder->orderBy('students.firstname', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Enroll student in session
     */
    public function enrollStudent($data)
    {
        // Check if student is already enrolled in this session
        $existing = $this->where('student_id', $data['student_id'])
                        ->where('session_id', $data['session_id'])
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Student is already enrolled in this session',
                'data' => null
            ];
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['is_leave'] = $data['is_leave'] ?? 0;
        $data['is_alumni'] = $data['is_alumni'] ?? 0;
        $data['default_login'] = $data['default_login'] ?? 0;
        $data['transport_fees'] = $data['transport_fees'] ?? 0.00;
        $data['fees_discount'] = $data['fees_discount'] ?? 0.00;

        return $this->createRecord($data);
    }

    /**
     * Promote students to next class
     */
    public function promoteStudents($fromClassId, $fromSectionId, $fromSessionId, $toClassId, $toSectionId, $toSessionId, $studentIds)
    {
        $this->db->transStart();

        try {
            $promotedCount = 0;

            foreach ($studentIds as $studentId) {
                // Deactivate current session
                $this->where('student_id', $studentId)
                     ->where('session_id', $fromSessionId)
                     ->set('is_active', 'no')
                     ->update();

                // Create new session record
                $newSessionData = [
                    'student_id' => $studentId,
                    'session_id' => $toSessionId,
                    'class_id' => $toClassId,
                    'section_id' => $toSectionId,
                    'is_active' => 'yes',
                    'is_leave' => 0,
                    'is_alumni' => 0,
                    'default_login' => 0,
                    'transport_fees' => 0.00,
                    'fees_discount' => 0.00
                ];

                $this->insert($newSessionData);
                $promotedCount++;
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to promote students',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => "$promotedCount students promoted successfully",
                'data' => ['promoted_count' => $promotedCount]
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => 'Error occurred during promotion: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get session statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total enrollments
        $stats['total'] = $this->countAllResults();
        
        // Active enrollments
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Students on leave
        $stats['on_leave'] = $this->where('is_leave', 1)->countAllResults();
        
        // Alumni students
        $stats['alumni'] = $this->where('is_alumni', 1)->countAllResults();
        
        // Enrollments by class
        $stats['by_class'] = $this->db->table($this->table)
                                     ->select('classes.class, COUNT(student_session.id) as count')
                                     ->join('classes', 'student_session.class_id = classes.id')
                                     ->where('student_session.is_active', 'yes')
                                     ->groupBy('classes.id')
                                     ->get()
                                     ->getResultArray();
        
        return $stats;
    }

    /**
     * Mark student as alumni
     */
    public function markAsAlumni($studentId, $sessionId)
    {
        return $this->where('student_id', $studentId)
                   ->where('session_id', $sessionId)
                   ->set(['is_alumni' => 1, 'is_active' => 'no'])
                   ->update();
    }

    /**
     * Transfer student to different class/section
     */
    public function transferStudent($studentId, $sessionId, $newClassId, $newSectionId)
    {
        return $this->where('student_id', $studentId)
                   ->where('session_id', $sessionId)
                   ->set(['class_id' => $newClassId, 'section_id' => $newSectionId])
                   ->update();
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }
}
