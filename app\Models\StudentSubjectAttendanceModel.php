<?php

namespace App\Models;

class StudentSubjectAttendanceModel extends BaseModel
{
    protected $table = 'student_subject_attendances';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'subject_id', 'attendence_type', 'date', 'remark'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'subject_id' => 'required|integer',
        'attendence_type' => 'required|in_list[Present,Absent,Late,Half Day]',
        'date' => 'required|valid_date',
        'remark' => 'permit_empty|max_length[500]'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'subject_id' => [
            'required' => 'Subject is required',
            'integer' => 'Invalid subject'
        ],
        'attendence_type' => [
            'required' => 'Attendance type is required',
            'in_list' => 'Invalid attendance type'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'remark' => [
            'max_length' => 'Remark cannot exceed 500 characters'
        ]
    ];

    protected $searchableColumns = ['remark'];
    protected $orderableColumns = ['id', 'date', 'attendence_type', 'created_at'];

    /**
     * Get subject attendance with details
     */
    public function getSubjectAttendanceWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_subject_attendances.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, sessions.session, subjects.name as subject_name')
                ->join('student_session', 'student_subject_attendances.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id')
                ->join('subjects', 'student_subject_attendances.subject_id = subjects.id');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['subject_id'])) {
            $builder->where('student_subject_attendances.subject_id', $filters['subject_id']);
        }

        if (!empty($filters['date'])) {
            $builder->where('student_subject_attendances.date', $filters['date']);
        }

        if (!empty($filters['attendance_type'])) {
            $builder->where('student_subject_attendances.attendence_type', $filters['attendance_type']);
        }

        return $builder->orderBy('student_subject_attendances.date', 'DESC');
    }

    /**
     * Get attendance types
     */
    public function getAttendanceTypes()
    {
        return [
            'Present' => 'Present',
            'Absent' => 'Absent',
            'Late' => 'Late',
            'Half Day' => 'Half Day'
        ];
    }

    /**
     * Mark bulk subject attendance
     */
    public function markBulkSubjectAttendance($data)
    {
        $date = $data['date'] ?? date('Y-m-d');
        $subjectId = $data['subject_id'] ?? null;
        $attendanceData = $data['attendance'] ?? [];

        if (empty($attendanceData) || empty($subjectId)) {
            return [
                'success' => false,
                'message' => 'No attendance data or subject provided',
                'errors' => []
            ];
        }

        $this->db->transStart();

        try {
            foreach ($attendanceData as $studentSessionId => $attendance) {
                // Check if attendance already exists for this date and subject
                $existing = $this->where('student_session_id', $studentSessionId)
                               ->where('subject_id', $subjectId)
                               ->where('date', $date)
                               ->first();

                $attendanceRecord = [
                    'student_session_id' => $studentSessionId,
                    'subject_id' => $subjectId,
                    'date' => $date,
                    'attendence_type' => $attendance['type'],
                    'remark' => $attendance['remark'] ?? null
                ];

                if ($existing) {
                    $this->update($existing['id'], $attendanceRecord);
                } else {
                    $this->insert($attendanceRecord);
                }
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to mark subject attendance',
                    'errors' => []
                ];
            }

            return [
                'success' => true,
                'message' => 'Subject attendance marked successfully',
                'data' => null
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => 'Error marking subject attendance: ' . $e->getMessage(),
                'errors' => []
            ];
        }
    }

    /**
     * Get subject attendance statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total attendance records
        $stats['total'] = $this->countAllResults();
        
        // Today's attendance
        $stats['today'] = $this->where('date', date('Y-m-d'))->countAllResults();
        
        // Present today
        $stats['present_today'] = $this->where('date', date('Y-m-d'))
                                      ->where('attendence_type', 'Present')
                                      ->countAllResults();
        
        // Absent today
        $stats['absent_today'] = $this->where('date', date('Y-m-d'))
                                     ->where('attendence_type', 'Absent')
                                     ->countAllResults();
        
        // This month attendance
        $stats['this_month'] = $this->where('MONTH(date)', date('m'))
                                   ->where('YEAR(date)', date('Y'))
                                   ->countAllResults();
        
        // Subjects with attendance today
        $stats['subjects_today'] = $this->db->table($this->table)
                                           ->select('subject_id')
                                           ->distinct()
                                           ->where('date', date('Y-m-d'))
                                           ->countAllResults();
        
        // Attendance percentage today
        if ($stats['today'] > 0) {
            $stats['attendance_percentage'] = round(($stats['present_today'] / $stats['today']) * 100, 2);
        } else {
            $stats['attendance_percentage'] = 0;
        }
        
        return $stats;
    }

    /**
     * Get student subject attendance history
     */
    public function getStudentSubjectAttendanceHistory($studentSessionId, $subjectId = null, $fromDate = null, $toDate = null)
    {
        $builder = $this->where('student_session_id', $studentSessionId);
        
        if ($subjectId) {
            $builder->where('subject_id', $subjectId);
        }
        
        if ($fromDate) {
            $builder->where('date >=', $fromDate);
        }
        
        if ($toDate) {
            $builder->where('date <=', $toDate);
        }
        
        return $builder->orderBy('date', 'DESC')->findAll();
    }

    /**
     * Get subject attendance report by date range
     */
    public function getSubjectAttendanceReport($fromDate, $toDate, $subjectId = null, $classId = null, $sectionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_subject_attendances.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, subjects.name as subject_name')
                ->join('student_session', 'student_subject_attendances.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('subjects', 'student_subject_attendances.subject_id = subjects.id')
                ->where('student_subject_attendances.date >=', $fromDate)
                ->where('student_subject_attendances.date <=', $toDate);

        if ($subjectId) {
            $builder->where('student_subject_attendances.subject_id', $subjectId);
        }

        if ($classId) {
            $builder->where('student_session.class_id', $classId);
        }

        if ($sectionId) {
            $builder->where('student_session.section_id', $sectionId);
        }

        return $builder->orderBy('student_subject_attendances.date', 'DESC')
                       ->orderBy('subjects.name', 'ASC')
                       ->orderBy('students.firstname', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get subject attendance summary by student
     */
    public function getSubjectAttendanceSummary($studentSessionId, $subjectId = null, $month = null, $year = null)
    {
        $builder = $this->where('student_session_id', $studentSessionId);
        
        if ($subjectId) {
            $builder->where('subject_id', $subjectId);
        }
        
        if ($month) {
            $builder->where('MONTH(date)', $month);
        }
        
        if ($year) {
            $builder->where('YEAR(date)', $year);
        }
        
        $records = $builder->findAll();
        
        $summary = [
            'total_classes' => count($records),
            'present' => 0,
            'absent' => 0,
            'late' => 0,
            'half_day' => 0
        ];
        
        foreach ($records as $record) {
            switch ($record['attendence_type']) {
                case 'Present':
                    $summary['present']++;
                    break;
                case 'Absent':
                    $summary['absent']++;
                    break;
                case 'Late':
                    $summary['late']++;
                    break;
                case 'Half Day':
                    $summary['half_day']++;
                    break;
            }
        }
        
        if ($summary['total_classes'] > 0) {
            $summary['attendance_percentage'] = round(($summary['present'] / $summary['total_classes']) * 100, 2);
        } else {
            $summary['attendance_percentage'] = 0;
        }
        
        return $summary;
    }

    /**
     * Get subject-wise attendance summary
     */
    public function getSubjectWiseAttendanceSummary($studentSessionId, $month = null, $year = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('subjects.name as subject_name, subjects.id as subject_id,
                         COUNT(*) as total_classes,
                         SUM(CASE WHEN attendence_type = "Present" THEN 1 ELSE 0 END) as present,
                         SUM(CASE WHEN attendence_type = "Absent" THEN 1 ELSE 0 END) as absent,
                         SUM(CASE WHEN attendence_type = "Late" THEN 1 ELSE 0 END) as late,
                         SUM(CASE WHEN attendence_type = "Half Day" THEN 1 ELSE 0 END) as half_day')
                ->join('subjects', 'student_subject_attendances.subject_id = subjects.id')
                ->where('student_session_id', $studentSessionId)
                ->groupBy('subjects.id, subjects.name');
        
        if ($month) {
            $builder->where('MONTH(date)', $month);
        }
        
        if ($year) {
            $builder->where('YEAR(date)', $year);
        }
        
        $results = $builder->get()->getResultArray();
        
        foreach ($results as &$result) {
            if ($result['total_classes'] > 0) {
                $result['attendance_percentage'] = round(($result['present'] / $result['total_classes']) * 100, 2);
            } else {
                $result['attendance_percentage'] = 0;
            }
        }
        
        return $results;
    }
}
