<?php

namespace App\Models;

class StudentTimelineModel extends BaseModel
{
    protected $table = 'student_timeline';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_id', 'title', 'timeline_date', 'description', 'document',
        'status', 'created_student_id', 'date'
    ];

    protected $validationRules = [
        'student_id' => 'required|integer',
        'title' => 'required|min_length[3]|max_length[200]',
        'timeline_date' => 'required|valid_date',
        'description' => 'required|min_length[10]',
        'status' => 'required|max_length[200]',
        'created_student_id' => 'required|integer',
        'date' => 'required|valid_date'
    ];

    protected $validationMessages = [
        'student_id' => [
            'required' => 'Student is required',
            'integer' => 'Invalid student selection'
        ],
        'title' => [
            'required' => 'Title is required',
            'min_length' => 'Title must be at least 3 characters long',
            'max_length' => 'Title cannot exceed 200 characters'
        ],
        'timeline_date' => [
            'required' => 'Timeline date is required',
            'valid_date' => 'Please enter a valid timeline date'
        ],
        'description' => [
            'required' => 'Description is required',
            'min_length' => 'Description must be at least 10 characters long'
        ],
        'status' => [
            'required' => 'Status is required',
            'max_length' => 'Status cannot exceed 200 characters'
        ],
        'created_student_id' => [
            'required' => 'Created by student ID is required',
            'integer' => 'Invalid created by student ID'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ]
    ];

    protected $searchableColumns = ['title', 'description', 'status'];
    protected $orderableColumns = ['id', 'title', 'timeline_date', 'status', 'created_at'];

    /**
     * Get timeline with student details
     */
    public function getTimelineWithStudentDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_timeline.*, 
                         students.firstname, students.lastname, students.admission_no,
                         creator.firstname as creator_firstname, creator.lastname as creator_lastname')
                ->join('students', 'student_timeline.student_id = students.id')
                ->join('students as creator', 'student_timeline.created_student_id = creator.id', 'left');

        // Apply filters
        if (!empty($filters['student_id'])) {
            $builder->where('student_timeline.student_id', $filters['student_id']);
        }

        if (!empty($filters['status'])) {
            $builder->where('student_timeline.status', $filters['status']);
        }

        if (!empty($filters['from_date'])) {
            $builder->where('student_timeline.timeline_date >=', $filters['from_date']);
        }

        if (!empty($filters['to_date'])) {
            $builder->where('student_timeline.timeline_date <=', $filters['to_date']);
        }

        return $builder->orderBy('student_timeline.timeline_date', 'DESC');
    }

    /**
     * Get student timeline
     */
    public function getStudentTimeline($studentId, $limit = null)
    {
        $builder = $this->where('student_id', $studentId)
                       ->orderBy('timeline_date', 'DESC');
        
        if ($limit) {
            $builder->limit($limit);
        }
        
        return $builder->findAll();
    }

    /**
     * Add timeline entry
     */
    public function addTimelineEntry($data)
    {
        // Set default values
        $data['date'] = $data['date'] ?? date('Y-m-d');
        $data['status'] = $data['status'] ?? 'Active';

        return $this->createRecord($data);
    }

    /**
     * Get timeline statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total timeline entries
        $stats['total'] = $this->countAllResults();
        
        // Entries this month
        $stats['this_month'] = $this->where('MONTH(timeline_date)', date('m'))
                                   ->where('YEAR(timeline_date)', date('Y'))
                                   ->countAllResults();
        
        // Entries this year
        $stats['this_year'] = $this->where('YEAR(timeline_date)', date('Y'))
                                  ->countAllResults();
        
        // Students with timeline entries
        $stats['students_with_timeline'] = $this->db->table($this->table)
                                                   ->select('student_id')
                                                   ->distinct()
                                                   ->countAllResults();
        
        // Most common statuses
        $stats['status_counts'] = $this->db->table($this->table)
                                          ->select('status, COUNT(*) as count')
                                          ->groupBy('status')
                                          ->orderBy('count', 'DESC')
                                          ->limit(5)
                                          ->get()
                                          ->getResultArray();
        
        return $stats;
    }

    /**
     * Get timeline by date range
     */
    public function getTimelineByDateRange($fromDate, $toDate, $studentId = null)
    {
        $builder = $this->where('timeline_date >=', $fromDate)
                       ->where('timeline_date <=', $toDate);
        
        if ($studentId) {
            $builder->where('student_id', $studentId);
        }
        
        return $builder->orderBy('timeline_date', 'DESC')->findAll();
    }

    /**
     * Get recent timeline entries
     */
    public function getRecentEntries($limit = 10)
    {
        return $this->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get timeline entries by status
     */
    public function getByStatus($status)
    {
        return $this->where('status', $status)
                   ->orderBy('timeline_date', 'DESC')
                   ->findAll();
    }

    /**
     * Get common timeline statuses
     */
    public function getCommonStatuses()
    {
        return [
            'Active',
            'Completed',
            'Pending',
            'In Progress',
            'Cancelled',
            'On Hold',
            'Approved',
            'Rejected',
            'Under Review'
        ];
    }

    /**
     * Get timeline entry types
     */
    public function getTimelineTypes()
    {
        return [
            'Academic' => [
                'Admission',
                'Enrollment',
                'Grade Promotion',
                'Academic Achievement',
                'Exam Result',
                'Assignment Submission',
                'Project Completion'
            ],
            'Disciplinary' => [
                'Behavior Incident',
                'Disciplinary Action',
                'Counseling Session',
                'Parent Meeting',
                'Suspension',
                'Warning'
            ],
            'Medical' => [
                'Medical Checkup',
                'Vaccination',
                'Health Issue',
                'Medical Leave',
                'Injury Report',
                'Medical Clearance'
            ],
            'Administrative' => [
                'Document Submission',
                'Fee Payment',
                'Address Change',
                'Contact Update',
                'Emergency Contact',
                'Transport Registration'
            ],
            'Extracurricular' => [
                'Sports Participation',
                'Competition Entry',
                'Club Membership',
                'Event Participation',
                'Award Recognition',
                'Leadership Role'
            ]
        ];
    }

    /**
     * Search timeline entries
     */
    public function searchTimeline($searchTerm, $studentId = null)
    {
        $builder = $this->groupStart()
                       ->like('title', $searchTerm)
                       ->orLike('description', $searchTerm)
                       ->orLike('status', $searchTerm)
                       ->groupEnd();
        
        if ($studentId) {
            $builder->where('student_id', $studentId);
        }
        
        return $builder->orderBy('timeline_date', 'DESC')->findAll();
    }

    /**
     * Get timeline summary for student
     */
    public function getStudentTimelineSummary($studentId)
    {
        $summary = [];
        
        // Total entries
        $summary['total_entries'] = $this->where('student_id', $studentId)->countAllResults();
        
        // Entries by year
        $summary['by_year'] = $this->db->table($this->table)
                                      ->select('YEAR(timeline_date) as year, COUNT(*) as count')
                                      ->where('student_id', $studentId)
                                      ->groupBy('YEAR(timeline_date)')
                                      ->orderBy('year', 'DESC')
                                      ->get()
                                      ->getResultArray();
        
        // Recent entries
        $summary['recent_entries'] = $this->getStudentTimeline($studentId, 5);
        
        // Status distribution
        $summary['status_distribution'] = $this->db->table($this->table)
                                                  ->select('status, COUNT(*) as count')
                                                  ->where('student_id', $studentId)
                                                  ->groupBy('status')
                                                  ->orderBy('count', 'DESC')
                                                  ->get()
                                                  ->getResultArray();
        
        return $summary;
    }
}
