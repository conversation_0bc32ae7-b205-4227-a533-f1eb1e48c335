<?php

namespace App\Models;

class StudentTransportFeesModel extends BaseModel
{
    protected $table = 'student_transport_fees';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'transport_fees_master_id', 'month', 'amount', 'amount_paid',
        'due_date', 'payment_date', 'payment_mode', 'payment_id', 'is_active'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'transport_fees_master_id' => 'required|integer',
        'month' => 'required|valid_date[Y-m]',
        'amount' => 'required|decimal|greater_than_equal_to[0]',
        'amount_paid' => 'permit_empty|decimal|greater_than_equal_to[0]',
        'due_date' => 'permit_empty|valid_date',
        'payment_date' => 'permit_empty|valid_date',
        'payment_mode' => 'permit_empty|max_length[50]',
        'payment_id' => 'permit_empty|max_length[100]',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'transport_fees_master_id' => [
            'required' => 'Transport fees master is required',
            'integer' => 'Invalid transport fees master'
        ],
        'month' => [
            'required' => 'Month is required',
            'valid_date' => 'Please enter a valid month (YYYY-MM format)'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'decimal' => 'Amount must be a valid decimal number',
            'greater_than_equal_to' => 'Amount must be greater than or equal to 0'
        ],
        'amount_paid' => [
            'decimal' => 'Paid amount must be a valid decimal number',
            'greater_than_equal_to' => 'Paid amount must be greater than or equal to 0'
        ],
        'due_date' => [
            'valid_date' => 'Please enter a valid due date'
        ],
        'payment_date' => [
            'valid_date' => 'Please enter a valid payment date'
        ]
    ];

    protected $searchableColumns = ['payment_mode', 'payment_id'];
    protected $orderableColumns = ['id', 'month', 'amount', 'amount_paid', 'due_date', 'payment_date'];

    /**
     * Get transport fees with student details
     */
    public function getTransportFeesWithStudentDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_transport_fees.*, students.firstname, students.lastname, students.admission_no,
                         classes.class, sections.section, sessions.session,
                         transport_fees_master.route_title, transport_fees_master.pickup_point')
                ->join('student_session', 'student_transport_fees.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id')
                ->join('transport_fees_master', 'student_transport_fees.transport_fees_master_id = transport_fees_master.id', 'left');

        // Apply filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['month'])) {
            $builder->where('student_transport_fees.month', $filters['month']);
        }

        if (!empty($filters['year'])) {
            $builder->where('YEAR(student_transport_fees.month)', $filters['year']);
        }

        if (!empty($filters['payment_status'])) {
            switch ($filters['payment_status']) {
                case 'paid':
                    $builder->where('student_transport_fees.amount <= COALESCE(student_transport_fees.amount_paid, 0)');
                    break;
                case 'partial':
                    $builder->where('COALESCE(student_transport_fees.amount_paid, 0) > 0');
                    $builder->where('student_transport_fees.amount > COALESCE(student_transport_fees.amount_paid, 0)');
                    break;
                case 'pending':
                    $builder->where('COALESCE(student_transport_fees.amount_paid, 0) = 0');
                    break;
            }
        }

        return $builder->orderBy('student_transport_fees.month', 'DESC');
    }

    /**
     * Get transport routes
     */
    public function getTransportRoutes()
    {
        // This would typically come from a transport_routes table
        return [
            'Route A - City Center',
            'Route B - North Zone',
            'Route C - South Zone',
            'Route D - East Zone',
            'Route E - West Zone',
            'Route F - Suburban Area'
        ];
    }

    /**
     * Get payment modes
     */
    public function getPaymentModes()
    {
        return [
            'Cash',
            'Bank Transfer',
            'Cheque',
            'Credit Card',
            'Debit Card',
            'Online Payment',
            'UPI',
            'Net Banking',
            'Other'
        ];
    }

    /**
     * Get transport fees statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total transport fees
        $stats['total_fees'] = $this->countAllResults();
        
        // Total amount
        $result = $this->selectSum('amount')->first();
        $stats['total_amount'] = $result['amount'] ?? 0;
        
        // Total paid amount
        $result = $this->selectSum('amount_paid')->first();
        $stats['total_paid'] = $result['amount_paid'] ?? 0;
        
        // Outstanding amount
        $stats['outstanding_amount'] = $stats['total_amount'] - $stats['total_paid'];
        
        // This month collections
        $result = $this->selectSum('amount_paid')
                      ->where('MONTH(payment_date)', date('m'))
                      ->where('YEAR(payment_date)', date('Y'))
                      ->first();
        $stats['this_month_collection'] = $result['amount_paid'] ?? 0;
        
        // Current month fees
        $currentMonth = date('Y-m');
        $stats['current_month_fees'] = $this->where('month', $currentMonth)->countAllResults();
        
        // Paid fees count
        $stats['paid_fees'] = $this->where('amount <= COALESCE(amount_paid, 0)')->countAllResults();
        
        // Pending fees count
        $stats['pending_fees'] = $this->where('COALESCE(amount_paid, 0) = 0')->countAllResults();
        
        // Partial fees count
        $stats['partial_fees'] = $this->where('COALESCE(amount_paid, 0) > 0')
                                     ->where('amount > COALESCE(amount_paid, 0)')
                                     ->countAllResults();
        
        // Students using transport
        $stats['students_using_transport'] = $this->db->table($this->table)
                                                     ->select('student_session_id')
                                                     ->distinct()
                                                     ->where('month', $currentMonth)
                                                     ->countAllResults();
        
        return $stats;
    }

    /**
     * Get student transport fee summary
     */
    public function getStudentTransportFeeSummary($studentSessionId)
    {
        $summary = [];
        
        // Total fees for student
        $summary['total_fees'] = $this->where('student_session_id', $studentSessionId)->countAllResults();
        
        // Total amount
        $result = $this->selectSum('amount')
                      ->where('student_session_id', $studentSessionId)
                      ->first();
        $summary['total_amount'] = $result['amount'] ?? 0;
        
        // Total paid
        $result = $this->selectSum('amount_paid')
                      ->where('student_session_id', $studentSessionId)
                      ->first();
        $summary['total_paid'] = $result['amount_paid'] ?? 0;
        
        // Outstanding balance
        $summary['outstanding_balance'] = $summary['total_amount'] - $summary['total_paid'];
        
        // Current month status
        $currentMonth = date('Y-m');
        $currentMonthFee = $this->where('student_session_id', $studentSessionId)
                               ->where('month', $currentMonth)
                               ->first();
        
        if ($currentMonthFee) {
            $summary['current_month_amount'] = $currentMonthFee['amount'];
            $summary['current_month_paid'] = $currentMonthFee['amount_paid'] ?? 0;
            $summary['current_month_balance'] = $currentMonthFee['amount'] - ($currentMonthFee['amount_paid'] ?? 0);
            $summary['current_month_due_date'] = $currentMonthFee['due_date'];
        } else {
            $summary['current_month_amount'] = 0;
            $summary['current_month_paid'] = 0;
            $summary['current_month_balance'] = 0;
            $summary['current_month_due_date'] = null;
        }
        
        // Recent payments
        $summary['recent_payments'] = $this->where('student_session_id', $studentSessionId)
                                          ->where('amount_paid >', 0)
                                          ->orderBy('payment_date', 'DESC')
                                          ->limit(5)
                                          ->findAll();
        
        return $summary;
    }

    /**
     * Calculate balance amount
     */
    public function calculateBalanceAmount($feeId)
    {
        $fee = $this->find($feeId);
        if (!$fee) {
            return 0;
        }
        
        $amount = $fee['amount'] ?? 0;
        $paidAmount = $fee['amount_paid'] ?? 0;
        
        return $amount - $paidAmount;
    }

    /**
     * Get overdue transport fees
     */
    public function getOverdueTransportFees()
    {
        return $this->db->table($this->table)
                       ->select('student_transport_fees.*, students.firstname, students.lastname, students.admission_no')
                       ->join('student_session', 'student_transport_fees.student_session_id = student_session.id')
                       ->join('students', 'student_session.student_id = students.id')
                       ->where('student_transport_fees.due_date <', date('Y-m-d'))
                       ->where('student_transport_fees.amount > COALESCE(student_transport_fees.amount_paid, 0)')
                       ->orderBy('student_transport_fees.due_date', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Process transport fee payment
     */
    public function processPayment($feeId, $paymentData)
    {
        $fee = $this->find($feeId);
        if (!$fee) {
            return [
                'success' => false,
                'message' => 'Transport fee record not found',
                'errors' => []
            ];
        }

        $amount = $fee['amount'] ?? 0;
        $currentPaid = $fee['amount_paid'] ?? 0;
        $newPayment = $paymentData['amount'] ?? 0;
        $newTotalPaid = $currentPaid + $newPayment;

        if ($newTotalPaid > $amount) {
            return [
                'success' => false,
                'message' => 'Payment amount exceeds the total fee amount',
                'errors' => ['amount' => 'Payment amount cannot exceed the balance amount']
            ];
        }

        $updateData = [
            'amount_paid' => $newTotalPaid,
            'payment_date' => $paymentData['payment_date'] ?? date('Y-m-d'),
            'payment_mode' => $paymentData['payment_mode'] ?? null,
            'payment_id' => $paymentData['payment_id'] ?? null
        ];

        if ($this->update($feeId, $updateData)) {
            return [
                'success' => true,
                'message' => 'Transport fee payment processed successfully',
                'data' => $this->find($feeId)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to process transport fee payment',
                'errors' => $this->errors()
            ];
        }
    }

    /**
     * Generate monthly transport fees
     */
    public function generateMonthlyFees($month, $transportFeesMasterId = null)
    {
        // This would typically generate fees for all students using transport
        // For now, return a basic structure
        return [
            'success' => true,
            'message' => 'Monthly transport fees generated successfully',
            'data' => [
                'month' => $month,
                'generated_count' => 0
            ]
        ];
    }
}
