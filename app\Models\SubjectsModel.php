<?php

namespace App\Models;

class SubjectsModel extends BaseModel
{
    protected $table = 'subjects';
    protected $primaryKey = 'id';
    protected $allowedFields = ['name', 'code', 'type', 'is_active'];

    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'code' => 'required|min_length[2]|max_length[100]|is_unique[subjects.code,id,{id}]',
        'type' => 'required|in_list[Theory,Practical,Both]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Subject name is required',
            'min_length' => 'Subject name must be at least 2 characters long',
            'max_length' => 'Subject name cannot exceed 100 characters'
        ],
        'code' => [
            'required' => 'Subject code is required',
            'min_length' => 'Subject code must be at least 2 characters long',
            'max_length' => 'Subject code cannot exceed 100 characters',
            'is_unique' => 'Subject code already exists'
        ],
        'type' => [
            'required' => 'Subject type is required',
            'in_list' => 'Subject type must be Theory, Practical, or Both'
        ]
    ];

    protected $searchableColumns = ['name', 'code', 'type'];
    protected $orderableColumns = ['id', 'name', 'code', 'type', 'is_active', 'created_at'];

    /**
     * Get subjects for dropdown
     */
    public function getForDropdown()
    {
        $subjects = $this->where('is_active', 'yes')
                        ->orderBy('name', 'ASC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($subjects as $subject) {
            $dropdown[$subject['id']] = $subject['name'] . ' (' . $subject['code'] . ')';
        }
        
        return $dropdown;
    }

    /**
     * Get subjects by type
     */
    public function getByType($type)
    {
        return $this->where('type', $type)
                   ->where('is_active', 'yes')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get subject statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total subjects
        $stats['total'] = $this->countAllResults();
        
        // Active subjects
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Subjects by type
        $stats['by_type'] = $this->select('type, COUNT(*) as count')
                               ->groupBy('type')
                               ->findAll();
        
        return $stats;
    }
}
