<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Edit Class
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>
                    <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
                </li>
                <li>
                    <a class="font-medium" href="<?= base_url($route_prefix) ?>">Classes /</a>
                </li>
                <li class="font-medium text-primary">Edit</li>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Form -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Edit Class: <?= esc($record['class']) ?>
        </h3>
    </div>
    
    <div class="p-7">
        <form id="edit-form" method="POST" action="<?= base_url($route_prefix . '/update/' . $record['id']) ?>">
            <?= csrf_field() ?>
            
            <div class="mb-5.5">
                <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="class">
                    Class Name <span class="text-meta-1">*</span>
                </label>
                <input
                    class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                    type="text"
                    name="class"
                    id="class"
                    value="<?= esc($record['class']) ?>"
                    placeholder="Enter class name (e.g., Grade 1, Class A)"
                    required
                />
                <div class="invalid-feedback text-meta-1 text-sm mt-1" id="class-error"></div>
            </div>

            <div class="mb-5.5">
                <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="is_active">
                    Status
                </label>
                <div class="relative z-20 bg-white dark:bg-form-input">
                    <select
                        class="relative z-20 w-full appearance-none rounded border border-stroke bg-transparent py-3 px-4.5 outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input"
                        name="is_active"
                        id="is_active"
                    >
                        <option value="yes" <?= $record['is_active'] === 'yes' ? 'selected' : '' ?>>Active</option>
                        <option value="no" <?= $record['is_active'] === 'no' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                    <span class="absolute top-1/2 right-4 z-10 -translate-y-1/2">
                        <i class="fas fa-chevron-down"></i>
                    </span>
                </div>
            </div>

            <div class="flex justify-end gap-4.5">
                <a
                    href="<?= base_url($route_prefix) ?>"
                    class="flex justify-center rounded border border-stroke py-2 px-6 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white"
                >
                    Cancel
                </a>
                <button
                    type="submit"
                    class="flex justify-center rounded bg-primary py-2 px-6 font-medium text-gray hover:bg-opacity-90"
                >
                    Update Class
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#edit-form').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.invalid-feedback').text('');
        $('.border-meta-1').removeClass('border-meta-1');
        
        const formData = $(this).serialize();
        
        $.post($(this).attr('action'), formData)
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    setTimeout(function() {
                        window.location.href = '<?= base_url($route_prefix) ?>';
                    }, 1500);
                } else {
                    showToast(response.message, 'error');
                    
                    // Display validation errors
                    if (response.errors) {
                        $.each(response.errors, function(field, message) {
                            $('#' + field + '-error').text(message);
                            $('#' + field).addClass('border-meta-1');
                        });
                    }
                }
            })
            .fail(function() {
                showToast('An error occurred while updating the class', 'error');
            });
    });
});
</script>
<?= $this->endSection() ?>
