<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Class Details
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>
                    <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
                </li>
                <li>
                    <a class="font-medium" href="<?= base_url($route_prefix) ?>">Classes /</a>
                </li>
                <li class="font-medium text-primary">View</li>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Class Details -->
<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Class Information -->
    <div class="lg:col-span-2">
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
                <div class="flex items-center justify-between">
                    <h3 class="font-medium text-black dark:text-white">
                        Class Information
                    </h3>
                    <div class="flex gap-2">
                        <a href="<?= base_url($route_prefix . '/edit/' . $record['id']) ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                            <i class="fas fa-edit mr-2"></i>
                            Edit
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="p-7">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Class Name
                        </label>
                        <p class="text-lg font-semibold text-black dark:text-white">
                            <?= esc($record['class']) ?>
                        </p>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Status
                        </label>
                        <?php if ($record['is_active'] === 'yes'): ?>
                            <span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">
                                Active
                            </span>
                        <?php else: ?>
                            <span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">
                                Inactive
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Created Date
                        </label>
                        <p class="text-black dark:text-white">
                            <?= date('M j, Y \a\t g:i A', strtotime($record['created_at'])) ?>
                        </p>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Last Updated
                        </label>
                        <p class="text-black dark:text-white">
                            <?= date('M j, Y \a\t g:i A', strtotime($record['updated_at'])) ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="lg:col-span-1">
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Quick Stats
                </h3>
            </div>
            
            <div class="p-7">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-black dark:text-white">Total Students</span>
                        <span class="text-lg font-bold text-primary">0</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-black dark:text-white">Active Students</span>
                        <span class="text-lg font-bold text-success">0</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-black dark:text-white">Sections</span>
                        <span class="text-lg font-bold text-warning">0</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-black dark:text-white">Teachers</span>
                        <span class="text-lg font-bold text-meta-5">0</span>
                    </div>
                </div>
                
                <div class="mt-6">
                    <a href="<?= base_url('admin/students?class_id=' . $record['id']) ?>" class="w-full inline-flex items-center justify-center rounded-md border border-primary px-4 py-2 text-center font-medium text-primary hover:bg-primary hover:text-white">
                        <i class="fas fa-users mr-2"></i>
                        View Students
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Students -->
<div class="mt-6">
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Recent Students in This Class
            </h3>
        </div>
        
        <div class="p-7">
            <div class="text-center py-8">
                <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray">
                    <i class="fas fa-users text-gray-400 text-xl"></i>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No students found</h3>
                <p class="mt-1 text-sm text-gray-500">Students enrolled in this class will appear here.</p>
                <div class="mt-6">
                    <a href="<?= base_url('admin/students/create?class_id=' . $record['id']) ?>" class="inline-flex items-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700">
                        <i class="fas fa-plus mr-2"></i>
                        Add Student
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
