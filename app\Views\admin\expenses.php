<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="sm:flex sm:items-center mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Expenses Management</h1>
        <p class="mt-2 text-sm text-gray-700">Track and manage all school expenses and expenditures.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button type="button" onclick="addExpense()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-plus mr-2"></i>
            Add Expense
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6">
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-red-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Expenses</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            $<?= number_format(array_sum(array_column($expenses ?? [], 'amount')), 2) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">This Month</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            $<?= number_format(array_sum(array_column(array_filter($expenses ?? [], function($e) { 
                                return date('Y-m', strtotime($e['date'])) === date('Y-m'); 
                            }), 'amount')), 2) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Records</dt>
                        <dd class="text-lg font-bold text-gray-900"><?= count($expenses ?? []) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= count(array_unique(array_column($expenses ?? [], 'exp_category'))) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expense Chart -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Monthly Expense Trends</h3>
        <p class="text-sm text-gray-500">Expense patterns over the past 12 months</p>
    </div>
    <div class="p-6">
        <canvas id="expenseChart" width="400" height="150"></canvas>
    </div>
</div>

<!-- Expenses Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">All Expenses</h3>
        <p class="text-sm text-gray-500">Complete record of all expenses with details</p>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="expensesTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expense</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($expenses)): ?>
                        <?php foreach ($expenses as $expense): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-receipt text-red-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?= esc($expense['name'] ?? 'N/A') ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: <?= $expense['id'] ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <?= esc($expense['exp_category'] ?? 'Uncategorized') ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">$<?= number_format($expense['amount'] ?? 0, 2) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= date('M j, Y', strtotime($expense['date'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= esc($expense['invoice_no'] ?? 'N/A') ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewExpense(<?= $expense['id'] ?>)" class="text-primary-600 hover:text-primary-900" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editExpense(<?= $expense['id'] ?>)" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if (!empty($expense['documents'])): ?>
                                            <button onclick="viewDocument('<?= $expense['documents'] ?>')" class="text-green-600 hover:text-green-900" title="View Document">
                                                <i class="fas fa-file-alt"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button onclick="deleteExpense(<?= $expense['id'] ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-receipt text-4xl mb-4"></i>
                                    <p>No expenses recorded yet. Add your first expense to get started.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#expensesTable').DataTable({
            responsive: true,
            order: [[3, 'desc']], // Sort by date descending
            columnDefs: [
                { orderable: false, targets: [5] } // Disable sorting on Actions column
            ]
        });
    });

    // Expense Chart
    const expenseCtx = document.getElementById('expenseChart').getContext('2d');
    const expenseChart = new Chart(expenseCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Monthly Expenses',
                data: [8000, 9500, 7200, 8800, 9200, 7800, 8500, 9800, 8200, 9000, 8700, 9300],
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Add Expense Function
    function addExpense() {
        Swal.fire({
            title: 'Add New Expense',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Expense Name</label>
                        <input type="text" id="expenseName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="expenseCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Select Category</option>
                            <option value="Office Supplies">Office Supplies</option>
                            <option value="Utilities">Utilities</option>
                            <option value="Maintenance">Maintenance</option>
                            <option value="Transportation">Transportation</option>
                            <option value="Equipment">Equipment</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                        <input type="number" id="expenseAmount" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                        <input type="date" id="expenseDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Invoice Number</label>
                        <input type="text" id="invoiceNo" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea id="expenseNote" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Expense',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const name = document.getElementById('expenseName').value;
                const category = document.getElementById('expenseCategory').value;
                const amount = document.getElementById('expenseAmount').value;
                const date = document.getElementById('expenseDate').value;
                const invoiceNo = document.getElementById('invoiceNo').value;
                const note = document.getElementById('expenseNote').value;

                if (!name || !category || !amount || !date) {
                    Swal.showValidationMessage('Name, category, amount, and date are required');
                    return false;
                }

                return { name, category, amount, date, invoiceNo, note };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Expense added successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // View Expense Function
    function viewExpense(id) {
        Swal.fire({
            title: 'Expense Details',
            html: `
                <div class="text-left">
                    <p><strong>Expense ID:</strong> ${id}</p>
                    <p><strong>Status:</strong> Loading...</p>
                    <p><strong>Payment Method:</strong> Loading...</p>
                    <p class="text-sm text-gray-500 mt-4">Full expense details would be loaded here via AJAX.</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Edit Expense Function
    function editExpense(id) {
        Swal.fire({
            title: 'Edit Expense',
            html: `
                <div class="text-left">
                    <p class="text-sm text-gray-500 mb-4">Expense ID: ${id}</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Expense Name</label>
                        <input type="text" id="editExpenseName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                        <input type="number" id="editExpenseAmount" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea id="editExpenseNote" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update Expense',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Expense updated successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // View Document Function
    function viewDocument(filename) {
        Swal.fire({
            title: 'Document',
            html: `
                <div class="text-center">
                    <i class="fas fa-file-alt text-6xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-900 mb-2">${filename}</p>
                    <p class="text-sm text-gray-500 mb-4">Document preview would be shown here</p>
                    <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        <i class="fas fa-download mr-2"></i>
                        Download
                    </a>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Delete Expense Function
    function deleteExpense(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Expense deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }
</script>
<?= $this->endSection() ?>
