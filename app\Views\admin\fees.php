<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="sm:flex sm:items-center mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Fees Management</h1>
        <p class="mt-2 text-sm text-gray-700">Manage fee structures, collections, and payment tracking.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button type="button" onclick="addFee()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-plus mr-2"></i>
            Add Fee Structure
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6">
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-green-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Collected</dt>
                        <dd class="text-lg font-bold text-gray-900">$<?= number_format(125000, 2) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                        <dd class="text-lg font-bold text-gray-900">$<?= number_format(25000, 2) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Overdue</dt>
                        <dd class="text-lg font-bold text-gray-900">$<?= number_format(5000, 2) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-list text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Fee Types</dt>
                        <dd class="text-lg font-bold text-gray-900"><?= count($fees ?? []) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fee Collection Chart -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Monthly Fee Collection</h3>
        <p class="text-sm text-gray-500">Fee collection trends over the past 12 months</p>
    </div>
    <div class="p-6">
        <canvas id="feeCollectionChart" width="400" height="150"></canvas>
    </div>
</div>

<!-- Fee Structures Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Fee Structures</h3>
        <p class="text-sm text-gray-500">All fee types and their amounts by class</p>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="feesTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fee Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($fees)): ?>
                        <?php foreach ($fees as $fee): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-money-bill text-green-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?= esc($fee['type'] ?? 'N/A') ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: <?= $fee['id'] ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= esc($fee['class'] ?? 'All Classes') ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">$<?= number_format($fee['amount'] ?? 0, 2) ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate">
                                        <?= esc($fee['description'] ?? 'No description') ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (($fee['is_active'] ?? 'no') === 'yes'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewFee(<?= $fee['id'] ?>)" class="text-primary-600 hover:text-primary-900" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editFee(<?= $fee['id'] ?>)" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteFee(<?= $fee['id'] ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-money-bill-wave text-4xl mb-4"></i>
                                    <p>No fee structures found. Create your first fee structure to get started.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#feesTable').DataTable({
            responsive: true,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [5] } // Disable sorting on Actions column
            ]
        });
    });

    // Fee Collection Chart
    const feeCtx = document.getElementById('feeCollectionChart').getContext('2d');
    const feeChart = new Chart(feeCtx, {
        type: 'bar',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Collected',
                data: [12000, 15000, 18000, 14000, 16000, 19000, 17000, 20000, 18500, 16500, 19500, 21000],
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgba(34, 197, 94, 1)',
                borderWidth: 1
            }, {
                label: 'Pending',
                data: [3000, 2500, 2000, 3500, 2800, 2200, 3200, 1800, 2600, 3100, 2400, 2000],
                backgroundColor: 'rgba(245, 158, 11, 0.8)',
                borderColor: 'rgba(245, 158, 11, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Add Fee Function
    function addFee() {
        Swal.fire({
            title: 'Add Fee Structure',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Fee Type</label>
                        <select id="feeType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Select Fee Type</option>
                            <option value="Tuition Fee">Tuition Fee</option>
                            <option value="Library Fee">Library Fee</option>
                            <option value="Lab Fee">Lab Fee</option>
                            <option value="Sports Fee">Sports Fee</option>
                            <option value="Transport Fee">Transport Fee</option>
                            <option value="Exam Fee">Exam Fee</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                        <select id="feeClass" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Select Class</option>
                            <option value="all">All Classes</option>
                            <option value="1">Class 1</option>
                            <option value="2">Class 2</option>
                            <option value="3">Class 3</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                        <input type="number" id="feeAmount" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="feeDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Fee',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const type = document.getElementById('feeType').value;
                const classId = document.getElementById('feeClass').value;
                const amount = document.getElementById('feeAmount').value;
                const description = document.getElementById('feeDescription').value;

                if (!type || !classId || !amount) {
                    Swal.showValidationMessage('Fee type, class, and amount are required');
                    return false;
                }

                return { type, classId, amount, description };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Fee structure added successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // View Fee Function
    function viewFee(id) {
        Swal.fire({
            title: 'Fee Details',
            html: `
                <div class="text-left">
                    <p><strong>Fee ID:</strong> ${id}</p>
                    <p><strong>Collection Status:</strong> Loading...</p>
                    <p><strong>Students Assigned:</strong> Loading...</p>
                    <p class="text-sm text-gray-500 mt-4">Full fee details would be loaded here via AJAX.</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Edit Fee Function
    function editFee(id) {
        Swal.fire({
            title: 'Edit Fee Structure',
            html: `
                <div class="text-left">
                    <p class="text-sm text-gray-500 mb-4">Fee ID: ${id}</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                        <input type="number" id="editFeeAmount" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="editFeeDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="editFeeStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="yes">Active</option>
                            <option value="no">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update Fee',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Fee structure updated successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // Delete Fee Function
    function deleteFee(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will affect all students with this fee structure!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Fee structure deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }
</script>
<?= $this->endSection() ?>
