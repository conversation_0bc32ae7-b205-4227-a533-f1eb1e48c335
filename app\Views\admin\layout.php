<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Dashboard' ?> | TailAdmin - Student Management System</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">

    <!-- Toastify CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="<?= base_url('assets/admin/css/admin.css') ?>">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9;
        }
        .sidebar-transition { transition: all 0.3s ease-in-out; }
        .content-transition { transition: all 0.3s ease-in-out; }
        .dark .sidebar-dark { background: linear-gradient(180deg, #1c2434 0%, #24303f 100%); }
        .sidebar-dark { background: linear-gradient(180deg, #1c2434 0%, #24303f 100%); }
        .menu-item:hover { background-color: rgba(59, 130, 246, 0.1); }
        .menu-item.active { background-color: #3b82f6; color: white; }
        .card-shadow { box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08); }
        .gradient-bg { background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%); }
    </style>

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        gray: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        dark: {
                            100: '#1c2434',
                            200: '#24303f',
                            300: '#313d4a',
                            400: '#3c4858',
                        }
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="h-full bg-gray-100 font-inter">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar-transition sidebar-dark fixed inset-y-0 left-0 z-50 w-72.5 transform -translate-x-full lg:translate-x-0 lg:static lg:inset-0">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5">
                <a href="<?= base_url('admin') ?>" class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                    </div>
                    <span class="text-xl font-bold text-white">TailAdmin</span>
                </a>

                <button class="block lg:hidden" id="sidebar-close">
                    <i class="fas fa-times text-white text-xl"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <div class="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
                <nav class="mt-5 py-4 px-4 lg:mt-9 lg:px-6">
                    <!-- Menu Group -->
                    <div>
                        <h3 class="mb-4 ml-4 text-sm font-semibold text-gray-400 uppercase">MENU</h3>

                        <ul class="mb-6 flex flex-col gap-1.5">
                            <!-- Dashboard -->
                            <li>
                                <a href="<?= base_url('admin/dashboard') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-tachometer-alt text-lg"></i>
                                    Dashboard
                                </a>
                            </li>

                            <!-- Students -->
                            <li>
                                <a href="<?= base_url('admin/students') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-user-graduate text-lg"></i>
                                    Students
                                </a>
                            </li>

                            <!-- Staff -->
                            <li>
                                <a href="<?= base_url('admin/staff') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-chalkboard-teacher text-lg"></i>
                                    Staff
                                </a>
                            </li>

                            <!-- Classes -->
                            <li>
                                <a href="<?= base_url('admin/classes') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-school text-lg"></i>
                                    Classes
                                </a>
                            </li>

                            <!-- Student Apps -->
                            <li>
                                <a href="<?= base_url('admin/student-apps') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-mobile-alt text-lg"></i>
                                    Student Apps
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Financial Group -->
                    <div>
                        <h3 class="mb-4 ml-4 text-sm font-semibold text-gray-400 uppercase">FINANCIAL</h3>

                        <ul class="mb-6 flex flex-col gap-1.5">
                            <!-- Fees -->
                            <li>
                                <a href="<?= base_url('admin/fees') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-dollar-sign text-lg"></i>
                                    Fees Management
                                </a>
                            </li>

                            <!-- Expenses -->
                            <li>
                                <a href="<?= base_url('admin/expenses') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-receipt text-lg"></i>
                                    Expenses
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Others Group -->
                    <div>
                        <h3 class="mb-4 ml-4 text-sm font-semibold text-gray-400 uppercase">OTHERS</h3>

                        <ul class="mb-6 flex flex-col gap-1.5">
                            <!-- Reports -->
                            <li>
                                <a href="<?= base_url('admin/reports') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-chart-bar text-lg"></i>
                                    Reports & Analytics
                                </a>
                            </li>

                            <!-- Settings -->
                            <li>
                                <a href="<?= base_url('admin/settings') ?>" class="nav-link menu-item group relative flex items-center gap-2.5 rounded-sm py-2 px-4 font-medium text-gray-300 duration-300 ease-in-out hover:bg-gray-700">
                                    <i class="fas fa-cog text-lg"></i>
                                    Settings
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Mobile sidebar overlay -->
        <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden"></div>

        <!-- Content Area -->
        <div class="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
            <!-- Header -->
            <header class="sticky top-0 z-999 flex w-full bg-white drop-shadow-1">
                <div class="flex flex-grow items-center justify-between py-4 px-4 shadow-2 md:px-6 2xl:px-11">
                    <div class="flex items-center gap-2 sm:gap-4 lg:hidden">
                        <!-- Hamburger Toggle BTN -->
                        <button id="sidebar-toggle" class="z-99999 block rounded-sm border border-stroke bg-white p-1.5 shadow-sm lg:hidden">
                            <span class="relative block h-5.5 w-5.5 cursor-pointer">
                                <span class="du-block absolute right-0 h-full w-full">
                                    <span class="relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-[0] duration-200 ease-in-out"></span>
                                    <span class="relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-150 duration-200 ease-in-out"></span>
                                    <span class="relative top-0 left-0 my-1 block h-0.5 w-0 rounded-sm bg-black delay-200 duration-200 ease-in-out"></span>
                                </span>
                                <span class="absolute right-0 h-full w-full rotate-45">
                                    <span class="absolute left-2.5 top-0 block h-full w-0.5 rounded-sm bg-black delay-300 duration-200 ease-in-out"></span>
                                    <span class="delay-400 absolute left-0 top-2.5 block h-0.5 w-full rounded-sm bg-black duration-200 ease-in-out"></span>
                                </span>
                            </span>
                        </button>
                    </div>

                    <div class="hidden sm:block">
                        <h1 class="text-title-md2 font-semibold text-black"><?= $title ?? 'Dashboard' ?></h1>
                    </div>

                    <div class="flex items-center gap-3 2xsm:gap-7">
                        <ul class="flex items-center gap-2 2xsm:gap-4">
                            <!-- Dark Mode Toggle -->
                            <li>
                                <button id="theme-toggle" class="relative flex h-8.5 w-8.5 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray hover:bg-opacity-10">
                                    <i class="fas fa-moon text-dark"></i>
                                </button>
                            </li>

                            <!-- Notification Menu Area -->
                            <li class="relative">
                                <button class="relative flex h-8.5 w-8.5 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray hover:bg-opacity-10">
                                    <span class="absolute -top-0.5 -right-0.5 z-1 h-2 w-2 rounded-full bg-meta-1">
                                        <span class="absolute -z-1 inline-flex h-full w-full animate-ping rounded-full bg-meta-1 opacity-75"></span>
                                    </span>
                                    <i class="fas fa-bell text-dark"></i>
                                </button>
                            </li>
                        </ul>

                        <!-- User Area -->
                        <div class="relative">
                            <button class="flex items-center gap-4" id="user-menu-button">
                                <span class="hidden text-right lg:block">
                                    <span class="block text-sm font-medium text-black">Admin User</span>
                                    <span class="block text-xs">Administrator</span>
                                </span>

                                <span class="h-12 w-12 rounded-full">
                                    <img class="h-12 w-12 rounded-full object-cover" src="https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff&size=48" alt="User">
                                </span>

                                <i class="fas fa-chevron-down text-sm text-gray-500"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main>
                <div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
                    <!-- Flash Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="mb-4 rounded-lg bg-green-50 p-4 text-green-800 border border-green-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">
                                        <?= esc(session()->getFlashdata('success')) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="mb-4 rounded-lg bg-red-50 p-4 text-red-800 border border-red-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">
                                        <?= esc(session()->getFlashdata('error')) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('warning')): ?>
                        <div class="mb-4 rounded-lg bg-yellow-50 p-4 text-yellow-800 border border-yellow-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">
                                        <?= esc(session()->getFlashdata('warning')) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('info')): ?>
                        <div class="mb-4 rounded-lg bg-blue-50 p-4 text-blue-800 border border-blue-200">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">
                                        <?= esc(session()->getFlashdata('info')) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?= $this->renderSection('content') ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Toast container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- Custom Admin JS -->
    <script src="<?= base_url('assets/admin/js/admin.js') ?>"></script>
    
    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        // Close sidebar when clicking overlay or close button
        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });

        // Close button functionality
        const sidebarClose = document.getElementById('sidebar-close');
        if (sidebarClose) {
            sidebarClose.addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
            });
        }

        // Dark mode toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', function() {
                document.documentElement.classList.toggle('dark');
                const isDark = document.documentElement.classList.contains('dark');
                localStorage.setItem('theme', isDark ? 'dark' : 'light');

                // Update icon
                const icon = this.querySelector('i');
                if (isDark) {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                } else {
                    icon.classList.remove('fa-sun');
                    icon.classList.add('fa-moon');
                }
            });
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            const icon = themeToggle?.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        }

        // Active navigation highlighting
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // Toast notification function
        function showToast(message, type = 'success') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6',
                stopOnFocus: true,
            }).showToast();
        }

        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('[class*="bg-green-50"], [class*="bg-red-50"], [class*="bg-yellow-50"], [class*="bg-blue-50"]');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.transition = 'opacity 0.5s ease-out';
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }, 5000);
            });
        });

        // Global DataTable configuration
        $.extend(true, $.fn.dataTable.defaults, {
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search...",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
