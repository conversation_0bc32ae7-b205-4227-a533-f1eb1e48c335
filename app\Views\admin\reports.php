<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="sm:flex sm:items-center mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Reports & Analytics</h1>
        <p class="mt-2 text-sm text-gray-700">Generate comprehensive reports and view analytics for better decision making.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button type="button" onclick="generateReport()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-file-export mr-2"></i>
            Generate Report
        </button>
    </div>
</div>

<!-- Quick Report Cards -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-8">
    <!-- Student Report -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200 cursor-pointer" onclick="generateStudentReport()">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <h3 class="text-lg font-medium text-gray-900">Student Report</h3>
                    <p class="text-sm text-gray-500">Enrollment, attendance, and performance</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">Last generated:</span>
                    <span class="text-gray-900">2 days ago</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Report -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200 cursor-pointer" onclick="generateFinancialReport()">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <h3 class="text-lg font-medium text-gray-900">Financial Report</h3>
                    <p class="text-sm text-gray-500">Fee collection and expenses</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">Last generated:</span>
                    <span class="text-gray-900">1 week ago</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Report -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200 cursor-pointer" onclick="generateStaffReport()">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chalkboard-teacher text-purple-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <h3 class="text-lg font-medium text-gray-900">Staff Report</h3>
                    <p class="text-sm text-gray-500">Staff performance and attendance</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">Last generated:</span>
                    <span class="text-gray-900">3 days ago</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Dashboard -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Students by Class Chart -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Students by Class</h3>
            <p class="text-sm text-gray-500">Distribution of students across different classes</p>
        </div>
        <div class="p-6">
            <canvas id="studentsClassChart" width="400" height="300"></canvas>
        </div>
    </div>

    <!-- Monthly Fee Collection -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Monthly Fee Collection</h3>
            <p class="text-sm text-gray-500">Fee collection trends over the year</p>
        </div>
        <div class="p-6">
            <canvas id="feeCollectionChart" width="400" height="300"></canvas>
        </div>
    </div>
</div>

<!-- Detailed Reports Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Available Reports</h3>
        <p class="text-sm text-gray-500">Generate and download detailed reports</p>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Student Reports -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="text-md font-medium text-gray-900 mb-3">Student Reports</h4>
                <div class="space-y-2">
                    <button onclick="downloadReport('student-list')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-list mr-2"></i>Student List
                    </button>
                    <button onclick="downloadReport('attendance')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-calendar-check mr-2"></i>Attendance Report
                    </button>
                    <button onclick="downloadReport('performance')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-chart-bar mr-2"></i>Performance Report
                    </button>
                </div>
            </div>

            <!-- Financial Reports -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="text-md font-medium text-gray-900 mb-3">Financial Reports</h4>
                <div class="space-y-2">
                    <button onclick="downloadReport('fee-collection')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-money-bill mr-2"></i>Fee Collection
                    </button>
                    <button onclick="downloadReport('expenses')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-receipt mr-2"></i>Expense Report
                    </button>
                    <button onclick="downloadReport('profit-loss')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-chart-line mr-2"></i>Profit & Loss
                    </button>
                </div>
            </div>

            <!-- Administrative Reports -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="text-md font-medium text-gray-900 mb-3">Administrative Reports</h4>
                <div class="space-y-2">
                    <button onclick="downloadReport('staff-list')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-users mr-2"></i>Staff List
                    </button>
                    <button onclick="downloadReport('class-summary')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-school mr-2"></i>Class Summary
                    </button>
                    <button onclick="downloadReport('system-usage')" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                        <i class="fas fa-chart-pie mr-2"></i>System Usage
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Reports -->
<div class="mt-8 bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recent Reports</h3>
        <p class="text-sm text-gray-500">Recently generated reports available for download</p>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">Monthly Student Report</div>
                            <div class="text-sm text-gray-500">December 2024</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                Student
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Dec 15, 2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            2.4 MB
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button onclick="downloadFile('student-report-dec.pdf')" class="text-primary-600 hover:text-primary-900" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button onclick="viewReport('student-report-dec.pdf')" class="text-green-600 hover:text-green-900" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="deleteReport('student-report-dec.pdf')" class="text-red-600 hover:text-red-900" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">Financial Summary</div>
                            <div class="text-sm text-gray-500">Q4 2024</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Financial
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Dec 10, 2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            1.8 MB
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button onclick="downloadFile('financial-q4.pdf')" class="text-primary-600 hover:text-primary-900" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button onclick="viewReport('financial-q4.pdf')" class="text-green-600 hover:text-green-900" title="View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="deleteReport('financial-q4.pdf')" class="text-red-600 hover:text-red-900" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Students by Class Chart
    const studentsCtx = document.getElementById('studentsClassChart').getContext('2d');
    const studentsChart = new Chart(studentsCtx, {
        type: 'doughnut',
        data: {
            labels: <?= json_encode(array_column($reports_data['students_by_class'] ?? [], 'class')) ?>,
            datasets: [{
                data: <?= json_encode(array_column($reports_data['students_by_class'] ?? [], 'student_count')) ?>,
                backgroundColor: [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(236, 72, 153, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Fee Collection Chart
    const feeCtx = document.getElementById('feeCollectionChart').getContext('2d');
    const feeChart = new Chart(feeCtx, {
        type: 'bar',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Fee Collection',
                data: <?= json_encode(array_column($reports_data['monthly_fees'] ?? [], 'total_collected')) ?>,
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgba(34, 197, 94, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Generate Report Functions
    function generateReport() {
        Swal.fire({
            title: 'Generate Custom Report',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                        <select id="reportType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Select Report Type</option>
                            <option value="student">Student Report</option>
                            <option value="financial">Financial Report</option>
                            <option value="staff">Staff Report</option>
                            <option value="attendance">Attendance Report</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="date" id="startDate" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <input type="date" id="endDate" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Format</label>
                        <select id="reportFormat" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Generate',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Report generation started. You will be notified when ready.', 'success');
            }
        });
    }

    function generateStudentReport() {
        showToast('Generating student report...', 'info');
        setTimeout(() => {
            showToast('Student report generated successfully!', 'success');
        }, 2000);
    }

    function generateFinancialReport() {
        showToast('Generating financial report...', 'info');
        setTimeout(() => {
            showToast('Financial report generated successfully!', 'success');
        }, 2000);
    }

    function generateStaffReport() {
        showToast('Generating staff report...', 'info');
        setTimeout(() => {
            showToast('Staff report generated successfully!', 'success');
        }, 2000);
    }

    function downloadReport(type) {
        showToast(`Downloading ${type} report...`, 'info');
        // Simulate download
        setTimeout(() => {
            showToast('Download completed!', 'success');
        }, 1500);
    }

    function downloadFile(filename) {
        showToast(`Downloading ${filename}...`, 'info');
        setTimeout(() => {
            showToast('Download completed!', 'success');
        }, 1500);
    }

    function viewReport(filename) {
        Swal.fire({
            title: 'Report Preview',
            html: `
                <div class="text-center">
                    <i class="fas fa-file-pdf text-6xl text-red-500 mb-4"></i>
                    <p class="text-lg font-medium text-gray-900 mb-2">${filename}</p>
                    <p class="text-sm text-gray-500 mb-4">Report preview would be shown here</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    function deleteReport(filename) {
        Swal.fire({
            title: 'Delete Report?',
            text: `Are you sure you want to delete ${filename}?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Report deleted successfully!', 'success');
            }
        });
    }
</script>
<?= $this->endSection() ?>
