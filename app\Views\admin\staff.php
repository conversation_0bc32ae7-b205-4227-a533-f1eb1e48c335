<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="sm:flex sm:items-center mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Staff Management</h1>
        <p class="mt-2 text-sm text-gray-700">Manage all staff members, teachers, and administrative personnel.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button type="button" onclick="addStaff()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-plus mr-2"></i>
            Add Staff
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6">
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Staff</dt>
                        <dd class="text-lg font-bold text-gray-900"><?= count($staff ?? []) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chalkboard-teacher text-green-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Teachers</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= count(array_filter($staff ?? [], function($s) { return ($s['designation'] ?? '') === 'Teacher'; })) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-tie text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Admin Staff</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= count(array_filter($staff ?? [], function($s) { return ($s['designation'] ?? '') !== 'Teacher'; })) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Staff</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= count(array_filter($staff ?? [], function($s) { return ($s['is_active'] ?? 'no') === 'yes'; })) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Staff Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">All Staff Members</h3>
        <p class="text-sm text-gray-500">Complete list of staff with their details and roles</p>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="staffTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Member</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($staff)): ?>
                        <?php foreach ($staff as $member): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" 
                                                 src="<?= !empty($member['image']) ? base_url('uploads/staff_images/' . $member['image']) : 'https://ui-avatars.com/api/?name=' . urlencode(($member['name'] ?? 'Staff')) . '&background=10b981&color=fff' ?>" 
                                                 alt="<?= esc($member['name'] ?? 'Staff') ?>">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?= esc($member['name'] ?? 'N/A') ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?= esc($member['email'] ?? 'No email') ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= esc($member['employee_id'] ?? 'N/A') ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= esc($member['designation'] ?? 'N/A') ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= esc($member['department'] ?? 'N/A') ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div><?= esc($member['contact_no'] ?? 'No phone') ?></div>
                                    <div class="text-gray-500"><?= esc($member['address'] ?? 'No address') ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (($member['is_active'] ?? 'no') === 'yes'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewStaff(<?= $member['id'] ?>)" class="text-primary-600 hover:text-primary-900" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editStaff(<?= $member['id'] ?>)" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteStaff(<?= $member['id'] ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#staffTable').DataTable({
            responsive: true,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6] } // Disable sorting on Actions column
            ]
        });
    });

    // Add Staff Function
    function addStaff() {
        Swal.fire({
            title: 'Add New Staff Member',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="staffName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                        <input type="text" id="employeeId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                        <select id="designation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">Select Designation</option>
                            <option value="Teacher">Teacher</option>
                            <option value="Principal">Principal</option>
                            <option value="Vice Principal">Vice Principal</option>
                            <option value="Administrator">Administrator</option>
                            <option value="Librarian">Librarian</option>
                            <option value="Accountant">Accountant</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="staffEmail" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <input type="tel" id="staffPhone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Staff',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const name = document.getElementById('staffName').value;
                const employeeId = document.getElementById('employeeId').value;
                const designation = document.getElementById('designation').value;
                const email = document.getElementById('staffEmail').value;
                const phone = document.getElementById('staffPhone').value;

                if (!name || !employeeId || !designation) {
                    Swal.showValidationMessage('Name, Employee ID, and Designation are required');
                    return false;
                }

                return { name, employeeId, designation, email, phone };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Staff member added successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // View Staff Function
    function viewStaff(id) {
        Swal.fire({
            title: 'Staff Details',
            html: `
                <div class="text-left">
                    <p><strong>Staff ID:</strong> ${id}</p>
                    <p><strong>Status:</strong> Loading...</p>
                    <p class="text-sm text-gray-500 mt-4">Full staff details would be loaded here via AJAX.</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Edit Staff Function
    function editStaff(id) {
        Swal.fire({
            title: 'Edit Staff Member',
            html: `
                <div class="text-left">
                    <p class="text-sm text-gray-500 mb-4">Staff ID: ${id}</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="editStaffName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                        <select id="editDesignation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="Teacher">Teacher</option>
                            <option value="Principal">Principal</option>
                            <option value="Vice Principal">Vice Principal</option>
                            <option value="Administrator">Administrator</option>
                            <option value="Librarian">Librarian</option>
                            <option value="Accountant">Accountant</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="editStaffStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="yes">Active</option>
                            <option value="no">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update Staff',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Staff member updated successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // Delete Staff Function
    function deleteStaff(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Staff member deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }
</script>
<?= $this->endSection() ?>
