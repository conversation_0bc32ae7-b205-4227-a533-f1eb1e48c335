<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Action Buttons -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <div class="flex flex-wrap gap-3">
        <button id="add-staff-btn" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-plus mr-2"></i>
            Add New Staff
        </button>
        <button id="import-staff-btn" class="inline-flex items-center justify-center rounded-md bg-green-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-upload mr-2"></i>
            Import Staff
        </button>
        <button id="attendance-btn" class="inline-flex items-center justify-center rounded-md bg-yellow-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-calendar-check mr-2"></i>
            Mark Attendance
        </button>
    </div>
    <div class="flex flex-wrap gap-3">
        <button id="export-csv-btn" class="inline-flex items-center justify-center rounded-md bg-gray-600 px-4 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-download mr-2"></i>
            Export CSV
        </button>
        <button id="bulk-delete-btn" class="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2.5 text-center font-medium text-white hover:bg-opacity-90" disabled>
            <i class="fas fa-trash mr-2"></i>
            Delete Selected
        </button>
    </div>
</div>

<!-- Filters -->
<div class="mb-6 rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
    <h3 class="mb-4 text-lg font-semibold text-black dark:text-white">Filters</h3>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Department</label>
            <select id="filter-department" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">All Departments</option>
                <!-- Options will be loaded dynamically -->
            </select>
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Designation</label>
            <select id="filter-designation" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">All Designations</option>
                <!-- Options will be loaded dynamically -->
            </select>
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Gender</label>
            <select id="filter-gender" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">All Genders</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
            </select>
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Status</label>
            <select id="filter-status" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">All Status</option>
                <option value="1">Active</option>
                <option value="0">Inactive</option>
            </select>
        </div>
    </div>
    <div class="mt-4 flex gap-3">
        <button id="apply-filters-btn" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-filter mr-2"></i>
            Apply Filters
        </button>
        <button id="clear-filters-btn" class="inline-flex items-center justify-center rounded-md bg-gray-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-times mr-2"></i>
            Clear Filters
        </button>
    </div>
</div>

<!-- Staff Table -->
<div class="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
    <div class="max-w-full overflow-x-auto">
        <table id="staff-table" class="w-full table-auto">
            <thead>
                <tr class="bg-gray-2 text-left dark:bg-meta-4">
                    <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">
                        <input type="checkbox" id="select-all" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
                    </th>
                    <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Photo</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Employee ID</th>
                    <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Name</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Department</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Designation</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Email</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Contact</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Gender</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                    <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be loaded via AJAX -->
            </tbody>
        </table>
    </div>
</div>

<!-- Statistics Cards -->
<div class="mt-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-users text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="total-staff" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Total Staff</span>
            </div>
        </div>
    </div>

    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-3 dark:bg-meta-4">
            <i class="fas fa-user-check text-green-600 text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="active-staff" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Active Staff</span>
            </div>
        </div>
    </div>

    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-5 dark:bg-meta-4">
            <i class="fas fa-building text-purple-600 text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="departments-count" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Departments</span>
            </div>
        </div>
    </div>

    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-6 dark:bg-meta-4">
            <i class="fas fa-user-plus text-blue-600 text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="recent-joinings" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Recent Joinings</span>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    const staffTable = $('#staff-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . "/get-data") ?>',
            type: 'POST',
            data: function(d) {
                d.department = $('#filter-department').val();
                d.designation = $('#filter-designation').val();
                d.gender = $('#filter-gender').val();
                d.is_active = $('#filter-status').val();
            }
        },
        columns: [
            {
                data: 'id',
                orderable: false,
                render: function(data) {
                    return '<input type="checkbox" class="row-checkbox h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" value="' + data + '">';
                }
            },
            {
                data: 'image',
                orderable: false,
                render: function(data, type, row) {
                    const imageUrl = data ? '<?= base_url("writable/uploads/staff/") ?>' + data : 'https://ui-avatars.com/api/?name=' + encodeURIComponent(row.name + ' ' + row.surname) + '&background=3b82f6&color=fff&size=40';
                    return '<img src="' + imageUrl + '" alt="Staff Photo" class="h-10 w-10 rounded-full object-cover">';
                }
            },
            { data: 'employee_id' },
            {
                data: null,
                render: function(data, type, row) {
                    return row.name + ' ' + (row.surname || '');
                }
            },
            { data: 'department_name', defaultContent: '-' },
            { data: 'designation', defaultContent: '-' },
            { data: 'email' },
            { data: 'contact_no' },
            { data: 'gender' },
            {
                data: 'is_active',
                render: function(data) {
                    return data == 1 
                        ? '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Active</span>'
                        : '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Inactive</span>';
                }
            },
            {
                data: 'id',
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <div class="flex items-center space-x-3.5">
                            <button class="view-btn hover:text-primary" data-id="${data}" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="edit-btn hover:text-primary" data-id="${data}" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="toggle-status-btn hover:text-warning" data-id="${data}" data-status="${row.is_active}" title="Toggle Status">
                                <i class="fas fa-toggle-${row.is_active == 1 ? 'on' : 'off'}"></i>
                            </button>
                            <button class="delete-btn hover:text-danger" data-id="${data}" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[2, 'desc']],
        drawCallback: function() {
            updateBulkDeleteButton();
        }
    });

    // Load statistics
    loadStatistics();

    // Event handlers
    $('#add-staff-btn').click(function() {
        window.location.href = '<?= base_url($route_prefix . "/create") ?>';
    });

    $('#import-staff-btn').click(function() {
        window.location.href = '<?= base_url($route_prefix . "/import") ?>';
    });

    $('#attendance-btn').click(function() {
        window.location.href = '<?= base_url($route_prefix . "/attendance") ?>';
    });

    $('#export-csv-btn').click(function() {
        window.location.href = '<?= base_url($route_prefix . "/export/csv") ?>';
    });

    // Filter handlers
    $('#apply-filters-btn').click(function() {
        staffTable.ajax.reload();
    });

    $('#clear-filters-btn').click(function() {
        $('#filter-department, #filter-designation, #filter-gender, #filter-status').val('');
        staffTable.ajax.reload();
    });

    // Row action handlers
    $(document).on('click', '.view-btn', function() {
        const id = $(this).data('id');
        window.location.href = '<?= base_url($route_prefix . "/show/") ?>' + id;
    });

    $(document).on('click', '.edit-btn', function() {
        const id = $(this).data('id');
        window.location.href = '<?= base_url($route_prefix . "/edit/") ?>' + id;
    });

    $(document).on('click', '.delete-btn', function() {
        const id = $(this).data('id');
        deleteStaff(id);
    });

    $(document).on('click', '.toggle-status-btn', function() {
        const id = $(this).data('id');
        toggleStatus(id);
    });

    // Bulk operations
    $('#select-all').change(function() {
        $('.row-checkbox').prop('checked', this.checked);
        updateBulkDeleteButton();
    });

    $(document).on('change', '.row-checkbox', function() {
        updateBulkDeleteButton();
    });

    $('#bulk-delete-btn').click(function() {
        const selectedIds = $('.row-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length === 0) {
            showToast('Please select staff to delete', 'error');
            return;
        }

        bulkDelete(selectedIds);
    });

    // Functions
    function loadStatistics() {
        $.get('<?= base_url($route_prefix . "/statistics") ?>')
            .done(function(response) {
                if (response.success) {
                    const stats = response.data;
                    $('#total-staff').text(stats.total || 0);
                    $('#active-staff').text(stats.active || 0);
                    $('#departments-count').text(stats.by_department?.length || 0);
                    $('#recent-joinings').text(stats.recent_joinings || 0);
                }
            });
    }

    function deleteStaff(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . "/delete/") ?>' + id,
                    type: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            staffTable.ajax.reload();
                            loadStatistics();
                        } else {
                            showToast(response.message, 'error');
                        }
                    }
                });
            }
        });
    }

    function toggleStatus(id) {
        $.post('<?= base_url($route_prefix . "/toggle-status/") ?>' + id)
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    staffTable.ajax.reload();
                    loadStatistics();
                } else {
                    showToast(response.message, 'error');
                }
            });
    }

    function bulkDelete(ids) {
        Swal.fire({
            title: 'Are you sure?',
            text: `You are about to delete ${ids.length} staff members. This action cannot be undone!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete them!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= base_url($route_prefix . "/bulk-delete") ?>', { ids: ids })
                    .done(function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            staffTable.ajax.reload();
                            loadStatistics();
                            $('#select-all').prop('checked', false);
                            updateBulkDeleteButton();
                        } else {
                            showToast(response.message, 'error');
                        }
                    });
            }
        });
    }

    function updateBulkDeleteButton() {
        const checkedCount = $('.row-checkbox:checked').length;
        $('#bulk-delete-btn').prop('disabled', checkedCount === 0);
    }
});
</script>
<?= $this->endSection() ?>
