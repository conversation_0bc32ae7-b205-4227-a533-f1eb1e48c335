<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Create Leave Application
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>
                    <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
                </li>
                <li>
                    <a class="font-medium" href="<?= base_url('admin/student-apps') ?>">Student Apps /</a>
                </li>
                <li>
                    <a class="font-medium" href="<?= base_url($route_prefix) ?>">Leave Applications /</a>
                </li>
                <li class="font-medium text-primary">Create</li>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Form -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Create New Leave Application
        </h3>
    </div>
    
    <div class="p-7">
        <form id="create-form" method="POST" action="<?= base_url($route_prefix . '/store') ?>" enctype="multipart/form-data">
            <?= csrf_field() ?>
            
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Student Selection -->
                <div class="md:col-span-2">
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="student_session_id">
                        Student <span class="text-meta-1">*</span>
                    </label>
                    <div class="relative z-20 bg-white dark:bg-form-input">
                        <select
                            class="relative z-20 w-full appearance-none rounded border border-stroke bg-transparent py-3 px-4.5 outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input"
                            name="student_session_id"
                            id="student_session_id"
                            required
                        >
                            <option value="">Select Student</option>
                            <?php if (!empty($student_sessions)): ?>
                                <?php foreach ($student_sessions as $id => $name): ?>
                                    <option value="<?= $id ?>"><?= esc($name) ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <span class="absolute top-1/2 right-4 z-10 -translate-y-1/2">
                            <i class="fas fa-chevron-down"></i>
                        </span>
                    </div>
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="student_session_id-error"></div>
                </div>

                <!-- From Date -->
                <div>
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="from_date">
                        From Date <span class="text-meta-1">*</span>
                    </label>
                    <input
                        class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        type="date"
                        name="from_date"
                        id="from_date"
                        required
                    />
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="from_date-error"></div>
                </div>

                <!-- To Date -->
                <div>
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="to_date">
                        To Date <span class="text-meta-1">*</span>
                    </label>
                    <input
                        class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        type="date"
                        name="to_date"
                        id="to_date"
                        required
                    />
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="to_date-error"></div>
                </div>

                <!-- Apply Date -->
                <div>
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="apply_date">
                        Apply Date <span class="text-meta-1">*</span>
                    </label>
                    <input
                        class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        type="date"
                        name="apply_date"
                        id="apply_date"
                        value="<?= date('Y-m-d') ?>"
                        required
                    />
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="apply_date-error"></div>
                </div>

                <!-- Request Type -->
                <div>
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="request_type">
                        Request Type
                    </label>
                    <div class="relative z-20 bg-white dark:bg-form-input">
                        <select
                            class="relative z-20 w-full appearance-none rounded border border-stroke bg-transparent py-3 px-4.5 outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input"
                            name="request_type"
                            id="request_type"
                        >
                            <option value="0" selected>Student Request</option>
                            <option value="1">Staff Request</option>
                        </select>
                        <span class="absolute top-1/2 right-4 z-10 -translate-y-1/2">
                            <i class="fas fa-chevron-down"></i>
                        </span>
                    </div>
                </div>

                <!-- Reason -->
                <div class="md:col-span-2">
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="reason">
                        Reason <span class="text-meta-1">*</span>
                    </label>
                    <textarea
                        class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        name="reason"
                        id="reason"
                        rows="4"
                        placeholder="Enter the reason for leave application..."
                        required
                    ></textarea>
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="reason-error"></div>
                    <p class="text-sm text-gray-500 mt-1">Minimum 10 characters, maximum 500 characters</p>
                </div>

                <!-- Document Upload -->
                <div class="md:col-span-2">
                    <label class="mb-3 block text-sm font-medium text-black dark:text-white" for="docs">
                        Supporting Documents
                    </label>
                    <input
                        class="w-full rounded border border-stroke bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        type="file"
                        name="docs"
                        id="docs"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    />
                    <div class="invalid-feedback text-meta-1 text-sm mt-1" id="docs-error"></div>
                    <p class="text-sm text-gray-500 mt-1">Allowed formats: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</p>
                </div>
            </div>

            <div class="flex justify-end gap-4.5 mt-6">
                <a
                    href="<?= base_url($route_prefix) ?>"
                    class="flex justify-center rounded border border-stroke py-2 px-6 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white"
                >
                    Cancel
                </a>
                <button
                    type="submit"
                    class="flex justify-center rounded bg-primary py-2 px-6 font-medium text-gray hover:bg-opacity-90"
                >
                    <i class="fas fa-save mr-2"></i>
                    Submit Application
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Date validation
    $('#from_date, #to_date').on('change', function() {
        validateDateRange();
    });

    function validateDateRange() {
        const fromDate = $('#from_date').val();
        const toDate = $('#to_date').val();
        
        if (fromDate && toDate) {
            if (new Date(fromDate) > new Date(toDate)) {
                $('#to_date-error').text('To date cannot be earlier than from date');
                $('#to_date').addClass('border-meta-1');
                return false;
            } else {
                $('#to_date-error').text('');
                $('#to_date').removeClass('border-meta-1');
                return true;
            }
        }
        return true;
    }

    // Character count for reason
    $('#reason').on('input', function() {
        const length = $(this).val().length;
        const maxLength = 500;
        const minLength = 10;
        
        if (length < minLength) {
            $(this).next('.invalid-feedback').text(`Minimum ${minLength} characters required (${length}/${minLength})`);
            $(this).addClass('border-meta-1');
        } else if (length > maxLength) {
            $(this).next('.invalid-feedback').text(`Maximum ${maxLength} characters allowed (${length}/${maxLength})`);
            $(this).addClass('border-meta-1');
        } else {
            $(this).next('.invalid-feedback').text('');
            $(this).removeClass('border-meta-1');
        }
    });

    // Form submission
    $('#create-form').on('submit', function(e) {
        e.preventDefault();
        
        // Validate date range
        if (!validateDateRange()) {
            showToast('Please correct the date range', 'error');
            return;
        }
        
        // Clear previous errors
        $('.invalid-feedback').text('');
        $('.border-meta-1').removeClass('border-meta-1');
        
        const formData = new FormData(this);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...');
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast(response.message, 'success');
                setTimeout(function() {
                    window.location.href = '<?= base_url($route_prefix) ?>';
                }, 1500);
            } else {
                showToast(response.message, 'error');
                
                // Display validation errors
                if (response.errors) {
                    $.each(response.errors, function(field, message) {
                        $('#' + field + '-error').text(message);
                        $('#' + field).addClass('border-meta-1');
                    });
                }
            }
        })
        .fail(function() {
            showToast('An error occurred while submitting the application', 'error');
        })
        .always(function() {
            $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save mr-2"></i>Submit Application');
        });
    });
});
</script>
<?= $this->endSection() ?>
