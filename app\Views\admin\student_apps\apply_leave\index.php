<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Leave Applications Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Applications -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-file-alt text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-applications">0</h4>
                <span class="text-sm font-medium">Total Applications</span>
            </div>
        </div>
    </div>

    <!-- Pending Applications -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-clock text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="pending-applications">0</h4>
                <span class="text-sm font-medium">Pending</span>
            </div>
        </div>
    </div>

    <!-- Approved Applications -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-check text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="approved-applications">0</h4>
                <span class="text-sm font-medium">Approved</span>
            </div>
        </div>
    </div>

    <!-- Rejected Applications -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-times text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="rejected-applications">0</h4>
                <span class="text-sm font-medium">Rejected</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Filters & Actions</h3>
            <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-plus mr-2"></i>
                Add Leave Application
            </a>
        </div>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Status</label>
                <select id="filter-status" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Status</option>
                    <option value="0">Pending</option>
                    <option value="1">Approved</option>
                    <option value="2">Rejected</option>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Class</label>
                <select id="filter-class" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Classes</option>
                    <?php foreach ($classes as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">From Date</label>
                <input type="date" id="filter-from-date" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">To Date</label>
                <input type="date" id="filter-to-date" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
            </div>
        </div>
        
        <div class="mt-4 flex gap-2">
            <button id="apply-filters" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-filter mr-2"></i>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                <i class="fas fa-times mr-2"></i>
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Leave Applications</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="leave-applications-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Student</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Admission No</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Class</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">From Date</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">To Date</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Apply Date</th>
                        <th class="min-w-[200px] py-4 px-4 font-medium text-black dark:text-white">Reason</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#leave-applications-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
                d.status = $('#filter-status').val();
                d.class_id = $('#filter-class').val();
                d.from_date = $('#filter-from-date').val();
                d.to_date = $('#filter-to-date').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'student_name', name: 'student_name' },
            { data: 'admission_no', name: 'admission_no' },
            { data: 'class_section', name: 'class_section' },
            { data: 'from_date', name: 'from_date' },
            { data: 'to_date', name: 'to_date' },
            { data: 'apply_date', name: 'apply_date' },
            { data: 'reason', name: 'reason' },
            { data: 'status', name: 'status', orderable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true
    });

    // Apply filters
    $('#apply-filters').click(function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#filter-status').val('');
        $('#filter-class').val('');
        $('#filter-from-date').val('');
        $('#filter-to-date').val('');
        table.ajax.reload();
    });

    // Approve leave function
    window.approveLeave = function(id) {
        Swal.fire({
            title: 'Approve Leave Application',
            text: 'Are you sure you want to approve this leave application?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, approve it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= base_url($route_prefix . '/approve') ?>/' + id, {
                    [csrf_token]: csrf_hash
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while approving the leave application', 'error');
                });
            }
        });
    };

    // Reject leave function
    window.rejectLeave = function(id) {
        Swal.fire({
            title: 'Reject Leave Application',
            text: 'Are you sure you want to reject this leave application?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, reject it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= base_url($route_prefix . '/reject') ?>/' + id, {
                    [csrf_token]: csrf_hash
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while rejecting the leave application', 'error');
                });
            }
        });
    };

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the leave application', 'error');
                });
            }
        });
    };

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#total-applications').text(response.data.total);
                    $('#pending-applications').text(response.data.pending);
                    $('#approved-applications').text(response.data.approved);
                    $('#rejected-applications').text(response.data.rejected);
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }
});
</script>
<?= $this->endSection() ?>
