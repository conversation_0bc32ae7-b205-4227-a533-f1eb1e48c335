<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>Create Student Behaviour<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-title-md2 font-semibold text-black dark:text-white">
            Create Student Behaviour
        </h2>
        <nav>
            <ol class="flex items-center gap-2">
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                        <li class="text-body-color">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Form Card -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Create New Student Behaviour
            </h3>
        </div>

        <form id="createForm" action="<?= base_url('admin/student-apps/behaviour/store') ?>" method="POST" enctype="multipart/form-data">
            <?= csrf_field() ?>
            <div class="p-6.5">
                <div class="mb-4.5 flex flex-col gap-6 xl:flex-row">
                    <!-- Student -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Student <span class="text-meta-1">*</span>
                        </label>
                        <select name="student_session_id" id="student_session_id" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                            <option value="">Select Student</option>
                            <!-- Options will be populated by controller -->
                        </select>
                    </div>
                    <!-- Incident Date -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Incident Date <span class="text-meta-1">*</span>
                        </label>
                        <input type="date" name="incident_date" id="incident_date" value="" placeholder="Enter Incident Date" 
                               class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    </div>
                </div>

                <div class="mb-4.5 flex flex-col gap-6 xl:flex-row">
                    <!-- Title -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Title <span class="text-meta-1">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="" placeholder="Enter Title" 
                               class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    </div>
                    <!-- Description -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Description <span class="text-meta-1">*</span>
                        </label>
                        <textarea name="description" id="description" rows="4" placeholder="Enter Description" 
                                  class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required></textarea>
                    </div>
                </div>

                <div class="mb-4.5 flex flex-col gap-6 xl:flex-row">
                    <!-- Action Taken -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Action Taken 
                        </label>
                        <textarea name="action_taken" id="action_taken" rows="4" placeholder="Enter Action Taken" 
                                  class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" ></textarea>
                    </div>
                </div>


                <!-- Action Buttons -->
                <div class="flex gap-4.5">
                    <button type="submit" class="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90">
                        <i class="fas fa-save mr-2"></i>
                        Create Student Behaviour
                    </button>
                    <a href="<?= base_url('admin/student-apps/behaviour') ?>" class="flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Show loading
        Swal.fire({
            title: 'Creating Student Behaviour...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: data.message || 'Student Behaviour created successfully'
                }).then(() => {
                    window.location.href = 'admin/student-apps/behaviour';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: data.message || 'Failed to create Student Behaviour'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'An unexpected error occurred'
            });
        });
    });
});
</script>
<?= $this->endSection() ?>