<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Student Behaviour Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Behaviours -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-star text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-behaviours">0</h4>
                <span class="text-sm font-medium">Total Behaviours</span>
            </div>
        </div>
    </div>

    <!-- Positive Behaviours -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-thumbs-up text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="positive-behaviours">0</h4>
                <span class="text-sm font-medium">Positive</span>
            </div>
        </div>
    </div>

    <!-- Negative Behaviours -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-thumbs-down text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="negative-behaviours">0</h4>
                <span class="text-sm font-medium">Negative</span>
            </div>
        </div>
    </div>

    <!-- Average Points -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-chart-line text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="average-points">0</h4>
                <span class="text-sm font-medium">Average Points</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Filters & Actions</h3>
            <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-plus mr-2"></i>
                Add Behaviour
            </a>
        </div>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Point Range</label>
                <select id="filter-point-range" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Behaviours</option>
                    <option value="positive">Positive (> 0)</option>
                    <option value="negative">Negative (< 0)</option>
                    <option value="neutral">Neutral (= 0)</option>
                </select>
            </div>
        </div>
        
        <div class="mt-4 flex gap-2">
            <button id="apply-filters" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-filter mr-2"></i>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                <i class="fas fa-times mr-2"></i>
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Behaviour Definitions</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="behaviour-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[200px] py-4 px-4 font-medium text-black dark:text-white">Title</th>
                        <th class="min-w-[300px] py-4 px-4 font-medium text-black dark:text-white">Description</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Points</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Impact Level</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Created</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#behaviour-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
                d.point_range = $('#filter-point-range').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'title', name: 'title' },
            { data: 'description', name: 'description' },
            { data: 'point', name: 'point', orderable: false },
            { data: 'impact_level', name: 'impact_level', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true
    });

    // Apply filters
    $('#apply-filters').click(function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#filter-point-range').val('');
        table.ajax.reload();
    });

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the behaviour', 'error');
                });
            }
        });
    };

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#total-behaviours').text(response.data.total);
                    $('#positive-behaviours').text(response.data.positive);
                    $('#negative-behaviours').text(response.data.negative);
                    $('#average-points').text(response.data.average_points);
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }
});
</script>
<?= $this->endSection() ?>
