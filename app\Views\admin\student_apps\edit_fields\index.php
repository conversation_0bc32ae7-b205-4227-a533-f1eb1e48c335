<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Student Edit Fields Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Fields -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-list text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-fields">0</h4>
                <span class="text-sm font-medium">Total Fields</span>
            </div>
        </div>
    </div>

    <!-- Active Fields -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-check-circle text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="active-fields">0</h4>
                <span class="text-sm font-medium">Active Fields</span>
            </div>
        </div>
    </div>

    <!-- Inactive Fields -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-times-circle text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="inactive-fields">0</h4>
                <span class="text-sm font-medium">Inactive Fields</span>
            </div>
        </div>
    </div>

    <!-- This Month -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-alt text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="this-month-fields">0</h4>
                <span class="text-sm font-medium">This Month</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Filters & Actions</h3>
            <div class="flex gap-2">
                <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Add Field
                </a>
                <button id="initialize-defaults" class="inline-flex items-center justify-center rounded-md bg-success px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-cog mr-2"></i>
                    Initialize Defaults
                </button>
            </div>
        </div>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Status</label>
                <select id="filter-status" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>
        
        <div class="mt-4 flex gap-2">
            <button id="apply-filters" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-filter mr-2"></i>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                <i class="fas fa-times mr-2"></i>
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Student Edit Fields Configuration</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="edit-fields-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[200px] py-4 px-4 font-medium text-black dark:text-white">Field Name</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Created Date</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#edit-fields-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
                d.status = $('#filter-status').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'status', name: 'status', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true
    });

    // Apply filters
    $('#apply-filters').click(function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#filter-status').val('');
        table.ajax.reload();
    });

    // Toggle status function
    window.toggleStatus = function(id) {
        Swal.fire({
            title: 'Toggle Field Status',
            text: 'Are you sure you want to change the status of this field?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, toggle it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= base_url($route_prefix . '/toggle-status') ?>/' + id, {
                    [csrf_token]: csrf_hash
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while toggling field status', 'error');
                });
            }
        });
    };

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the field', 'error');
                });
            }
        });
    };

    // Initialize default fields
    $('#initialize-defaults').click(function() {
        Swal.fire({
            title: 'Initialize Default Fields',
            text: 'This will create default student fields if they don\'t exist. Continue?',
            icon: 'info',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, initialize!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('<?= base_url($route_prefix . '/initialize-defaults') ?>', {
                    [csrf_token]: csrf_hash
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while initializing default fields', 'error');
                });
            }
        });
    });

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#total-fields').text(response.data.total);
                    $('#active-fields').text(response.data.active);
                    $('#inactive-fields').text(response.data.inactive);
                    $('#this-month-fields').text(response.data.this_month);
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }
});
</script>
<?= $this->endSection() ?>
