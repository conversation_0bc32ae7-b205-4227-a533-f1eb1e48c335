<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>View Student Fees<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-title-md2 font-semibold text-black dark:text-white">
            View Student Fees
        </h2>
        <nav>
            <ol class="flex items-center gap-2">
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                        <li class="text-body-color">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Details Card -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Student Fees Details
            </h3>
        </div>

        <div class="p-6.5">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Details will be displayed here -->
                <?php foreach ($record as $key => $value): ?>
                    <div class="mb-4">
                        <label class="mb-2.5 block text-black dark:text-white font-medium">
                            <?= ucwords(str_replace('_', ' ', $key)) ?>
                        </label>
                        <div class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition dark:border-form-strokedark dark:bg-form-input">
                            <?= $value ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex gap-4.5">
                <a href="<?= base_url('admin/student-apps/fees/edit/' . $record['id']) ?>" class="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Student Fees
                </a>
                <a href="<?= base_url('admin/student-apps/fees') ?>" class="flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>