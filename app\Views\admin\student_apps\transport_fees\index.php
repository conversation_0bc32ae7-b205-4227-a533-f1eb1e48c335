<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Student Transport Fees Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Amount -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-bus text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-amount">₹0</h4>
                <span class="text-sm font-medium">Total Amount</span>
            </div>
        </div>
    </div>

    <!-- Paid Amount -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-check-circle text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="paid-amount">₹0</h4>
                <span class="text-sm font-medium">Paid Amount</span>
            </div>
        </div>
    </div>

    <!-- Outstanding Amount -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-exclamation-circle text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="outstanding-amount">₹0</h4>
                <span class="text-sm font-medium">Outstanding</span>
            </div>
        </div>
    </div>

    <!-- Students Using Transport -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-users text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="students-using-transport">0</h4>
                <span class="text-sm font-medium">Students</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Filters & Actions</h3>
            <div class="flex gap-2">
                <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Add Transport Fee
                </a>
                <button id="generate-monthly" class="inline-flex items-center justify-center rounded-md bg-success px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-calendar mr-2"></i>
                    Generate Monthly
                </button>
            </div>
        </div>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Class</label>
                <select id="filter-class" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Classes</option>
                    <?php foreach ($classes as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Section</label>
                <select id="filter-section" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Sections</option>
                    <?php foreach ($sections as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Month</label>
                <input type="month" id="filter-month" value="<?= date('Y-m') ?>" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Payment Status</label>
                <select id="filter-payment-status" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Status</option>
                    <option value="paid">Paid</option>
                    <option value="partial">Partial</option>
                    <option value="pending">Pending</option>
                </select>
            </div>
        </div>
        
        <div class="mt-4 flex gap-2">
            <button id="apply-filters" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-filter mr-2"></i>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                <i class="fas fa-times mr-2"></i>
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Student Transport Fees Records</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="transport-fees-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Student</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Admission No</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Class</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Month</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Amount</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Paid</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Balance</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Due Date</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#transport-fees-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
                d.class_id = $('#filter-class').val();
                d.section_id = $('#filter-section').val();
                d.month = $('#filter-month').val();
                d.payment_status = $('#filter-payment-status').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'student_name', name: 'student_name' },
            { data: 'admission_no', name: 'admission_no' },
            { data: 'class_section', name: 'class_section' },
            { data: 'month_year', name: 'month_year' },
            { data: 'amount', name: 'amount', className: 'text-right' },
            { data: 'paid_amount', name: 'paid_amount', className: 'text-right' },
            { data: 'balance', name: 'balance', className: 'text-right' },
            { data: 'due_date', name: 'due_date' },
            { data: 'payment_status', name: 'payment_status', orderable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true,
        scrollX: true
    });

    // Apply filters
    $('#apply-filters').click(function() {
        table.ajax.reload();
        loadStats();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#filter-class').val('');
        $('#filter-section').val('');
        $('#filter-month').val('<?= date('Y-m') ?>');
        $('#filter-payment-status').val('');
        table.ajax.reload();
        loadStats();
    });

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the transport fee record', 'error');
                });
            }
        });
    };

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#total-amount').text('₹' + numberFormat(response.data.total_amount));
                    $('#paid-amount').text('₹' + numberFormat(response.data.total_paid));
                    $('#outstanding-amount').text('₹' + numberFormat(response.data.outstanding_amount));
                    $('#students-using-transport').text(response.data.students_using_transport);
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }

    function numberFormat(num) {
        return parseFloat(num).toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
});
</script>
<?= $this->endSection() ?>
