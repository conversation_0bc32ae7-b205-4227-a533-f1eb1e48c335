<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Students Management
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <li>
                <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
            </li>
            <li class="font-medium text-primary">Students</li>
        </ol>
    </nav>
</div>

<!-- Page Header -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="font-medium text-black dark:text-white">Students Overview</h3>
                <p class="text-sm text-body">Manage all student records, enrollments, and information.</p>
            </div>
            <button type="button" onclick="addStudent()" class="inline-flex items-center justify-center rounded-md bg-primary py-4 px-10 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10">
                <i class="fas fa-plus mr-2"></i>
                Add Student
            </button>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-3 2xl:gap-7.5 mb-8">
    <!-- Total Students -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-graduate text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= count($students ?? []) ?>
                </h4>
                <span class="text-sm font-medium">Total Students</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-3">
                <i class="fas fa-arrow-up text-xs"></i>
                2.59%
            </span>
        </div>
    </div>

    <!-- Active Students -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-check text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= count(array_filter($students ?? [], function($s) { return $s['is_active'] === 'yes'; })) ?>
                </h4>
                <span class="text-sm font-medium">Active Students</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-3">
                <i class="fas fa-arrow-up text-xs"></i>
                4.35%
            </span>
        </div>
    </div>

    <!-- New This Month -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-plus text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= count(array_filter($students ?? [], function($s) {
                        return date('Y-m', strtotime($s['created_at'])) === date('Y-m');
                    })) ?>
                </h4>
                <span class="text-sm font-medium">New This Month</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-3">
                <i class="fas fa-arrow-up text-xs"></i>
                1.10%
            </span>
        </div>
    </div>
</div>

<!-- Students Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">All Students</h3>
        <p class="text-sm text-body">Complete list of students with their details</p>
    </div>
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="studentsTable" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[220px] py-4 px-4 font-medium text-black dark:text-white xl:pl-11">
                            Student
                        </th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">
                            Admission No
                        </th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                            Class
                        </th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                            Contact
                        </th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                            Status
                        </th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($students)): ?>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td class="border-b border-[#eee] py-5 px-4 pl-9 dark:border-strokedark xl:pl-11">
                                    <div class="flex items-center gap-3">
                                        <div class="flex-shrink-0">
                                            <img class="h-12 w-12 rounded-full object-cover"
                                                 src="<?= !empty($student['image']) ? base_url('uploads/student_images/' . $student['image']) : 'https://ui-avatars.com/api/?name=' . urlencode($student['firstname'] . ' ' . $student['lastname']) . '&background=3c50e0&color=fff&size=48' ?>"
                                                 alt="<?= esc($student['firstname'] . ' ' . $student['lastname']) ?>">
                                        </div>
                                        <div>
                                            <h5 class="font-medium text-black dark:text-white">
                                                <?= esc($student['firstname'] . ' ' . $student['lastname']) ?>
                                            </h5>
                                            <p class="text-sm text-body">
                                                <?= esc($student['email'] ?? 'No email') ?>
                                            </p>
                                        </div>
                                    </div>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <p class="text-black dark:text-white">
                                        <?= esc($student['admission_no'] ?? 'N/A') ?>
                                    </p>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <p class="text-black dark:text-white">
                                        <?= esc(($student['class'] ?? 'N/A') . ' - ' . ($student['section'] ?? '')) ?>
                                    </p>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <div>
                                        <p class="text-black dark:text-white"><?= esc($student['mobileno'] ?? 'No phone') ?></p>
                                        <p class="text-sm text-body"><?= esc($student['father_name'] ?? 'No guardian') ?></p>
                                    </div>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <?php if (($student['is_active'] ?? 'no') === 'yes'): ?>
                                        <p class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">
                                            Active
                                        </p>
                                    <?php else: ?>
                                        <p class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">
                                            Inactive
                                        </p>
                                    <?php endif; ?>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <div class="flex items-center space-x-3.5">
                                        <button onclick="viewStudent(<?= $student['id'] ?>)" class="hover:text-primary" title="View">
                                            <i class="fas fa-eye text-lg"></i>
                                        </button>
                                        <button onclick="editStudent(<?= $student['id'] ?>)" class="hover:text-primary" title="Edit">
                                            <i class="fas fa-edit text-lg"></i>
                                        </button>
                                        <button onclick="deleteStudent(<?= $student['id'] ?>)" class="hover:text-primary" title="Delete">
                                            <i class="fas fa-trash text-lg"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="border-b border-[#eee] py-12 text-center dark:border-strokedark">
                                <div class="text-body">
                                    <i class="fas fa-user-graduate text-4xl mb-4 text-gray-400"></i>
                                    <p>No students found. Add your first student to get started.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#studentsTable').DataTable({
            responsive: true,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [5] } // Disable sorting on Actions column
            ]
        });
    });

    // Add Student Function
    function addStudent() {
        Swal.fire({
            title: 'Add New Student',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" id="firstName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" id="lastName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <input type="tel" id="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Student',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const firstName = document.getElementById('firstName').value;
                const lastName = document.getElementById('lastName').value;
                const email = document.getElementById('email').value;
                const phone = document.getElementById('phone').value;

                if (!firstName || !lastName) {
                    Swal.showValidationMessage('First name and last name are required');
                    return false;
                }

                return {
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    phone: phone
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Here you would typically send an AJAX request to save the student
                showToast('Student added successfully!', 'success');
                // Reload the page or update the table
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        });
    }

    // View Student Function
    function viewStudent(id) {
        Swal.fire({
            title: 'Student Details',
            html: `
                <div class="text-left">
                    <p><strong>Student ID:</strong> ${id}</p>
                    <p><strong>Status:</strong> Loading...</p>
                    <p class="text-sm text-gray-500 mt-4">Full student details would be loaded here via AJAX.</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Edit Student Function
    function editStudent(id) {
        Swal.fire({
            title: 'Edit Student',
            html: `
                <div class="text-left">
                    <p class="text-sm text-gray-500 mb-4">Student ID: ${id}</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" id="editFirstName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" id="editLastName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="editStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="yes">Active</option>
                            <option value="no">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update Student',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Student updated successfully!', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        });
    }

    // Delete Student Function
    function deleteStudent(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                // Here you would typically send an AJAX request to delete the student
                showToast('Student deleted successfully!', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        });
    }
</script>
<?= $this->endSection() ?>
