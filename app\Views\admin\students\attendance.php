<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<?php if (isset($student)): ?>
<!-- Student Info Card -->
<div class="mb-6 rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="flex items-center gap-4">
        <?php 
        $imageUrl = !empty($student['image']) 
            ? base_url('writable/uploads/students/' . $student['image'])
            : 'https://ui-avatars.com/api/?name=' . urlencode($student['firstname'] . ' ' . $student['lastname']) . '&background=3b82f6&color=fff&size=60';
        ?>
        <img src="<?= $imageUrl ?>" alt="Student Photo" class="h-15 w-15 rounded-full object-cover">
        <div>
            <h3 class="text-lg font-semibold text-black dark:text-white">
                <?= esc($student['firstname'] . ' ' . ($student['lastname'] ?? '')) ?>
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Admission No: <?= esc($student['admission_no'] ?? 'N/A') ?>
            </p>
        </div>
    </div>
</div>

<!-- Attendance Summary -->
<?php if (isset($attendance_summary) && !empty($attendance_summary)): ?>
<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
    <?php foreach ($attendance_summary as $summary): ?>
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-check text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white"><?= $summary['count'] ?></h4>
                <span class="text-sm font-medium"><?= esc($summary['type']) ?></span>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>
<?php endif; ?>

<!-- Attendance Filters -->
<div class="mb-6 rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
    <h3 class="mb-4 text-lg font-semibold text-black dark:text-white">Attendance Filters</h3>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Class</label>
            <select id="filter-class" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">Select Class</option>
                <?php foreach ($classes as $id => $name): ?>
                    <option value="<?= $id ?>"><?= $name ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Section</label>
            <select id="filter-section" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">Select Section</option>
                <?php foreach ($sections as $id => $name): ?>
                    <option value="<?= $id ?>"><?= $name ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Date From</label>
            <input type="date" id="filter-date-from" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Date To</label>
            <input type="date" id="filter-date-to" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
        </div>
        <div>
            <label class="mb-2.5 block text-black dark:text-white">Attendance Type</label>
            <select id="filter-attendance-type" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                <option value="">All Types</option>
                <?php foreach ($attendance_types as $id => $type): ?>
                    <option value="<?= $id ?>"><?= $type ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="mt-4 flex gap-3">
        <button id="apply-filters-btn" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-filter mr-2"></i>
            Apply Filters
        </button>
        <button id="clear-filters-btn" class="inline-flex items-center justify-center rounded-md bg-gray-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-times mr-2"></i>
            Clear Filters
        </button>
        <button id="mark-attendance-btn" class="inline-flex items-center justify-center rounded-md bg-green-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
            <i class="fas fa-calendar-check mr-2"></i>
            Mark Attendance
        </button>
    </div>
</div>

<!-- Attendance Table -->
<div class="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h4 class="text-xl font-semibold text-black dark:text-white">
            Attendance Records
        </h4>
    </div>
    
    <div class="max-w-full overflow-x-auto">
        <table id="attendance-table" class="w-full table-auto">
            <thead>
                <tr class="bg-gray-2 text-left dark:bg-meta-4">
                    <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Student Name</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Admission No</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Class</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Section</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Date</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Attendance Type</th>
                    <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Remark</th>
                </tr>
            </thead>
            <tbody id="attendance-tbody">
                <!-- Data will be loaded via AJAX -->
            </tbody>
        </table>
    </div>
</div>

<!-- Mark Attendance Modal -->
<div id="mark-attendance-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-4xl rounded-lg bg-white p-6 dark:bg-boxdark max-h-[90vh] overflow-y-auto">
        <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-black dark:text-white">Mark Attendance</h3>
            <button id="close-attendance-modal" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="mark-attendance-form">
            <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Class <span class="text-meta-1">*</span></label>
                    <select id="attendance-class" name="class_id" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                        <option value="">Select Class</option>
                        <?php foreach ($classes as $id => $name): ?>
                            <option value="<?= $id ?>"><?= $name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Section <span class="text-meta-1">*</span></label>
                    <select id="attendance-section" name="section_id" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                        <option value="">Select Section</option>
                        <?php foreach ($sections as $id => $name): ?>
                            <option value="<?= $id ?>"><?= $name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Date <span class="text-meta-1">*</span></label>
                    <input type="date" name="date" value="<?= date('Y-m-d') ?>" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                </div>
            </div>
            
            <div class="mb-4">
                <button type="button" id="load-students-btn" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-users mr-2"></i>
                    Load Students
                </button>
            </div>
            
            <div id="students-attendance-list" class="hidden">
                <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Mark Attendance for Students</h4>
                <div class="max-h-60 overflow-y-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-2 text-left dark:bg-meta-4">
                                <th class="py-3 px-4 font-medium text-black dark:text-white">Student Name</th>
                                <th class="py-3 px-4 font-medium text-black dark:text-white">Admission No</th>
                                <th class="py-3 px-4 font-medium text-black dark:text-white">Attendance</th>
                            </tr>
                        </thead>
                        <tbody id="students-list">
                            <!-- Students will be loaded here -->
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-6 flex gap-3">
                    <button type="submit" class="flex-1 rounded bg-primary p-3 font-medium text-white hover:bg-opacity-90">
                        Save Attendance
                    </button>
                    <button type="button" id="cancel-attendance" class="flex-1 rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load attendance data
    loadAttendanceData();

    // Apply filters
    $('#apply-filters-btn').click(function() {
        loadAttendanceData();
    });

    // Clear filters
    $('#clear-filters-btn').click(function() {
        $('#filter-class, #filter-section, #filter-date-from, #filter-date-to, #filter-attendance-type').val('');
        loadAttendanceData();
    });

    // Show mark attendance modal
    $('#mark-attendance-btn').click(function() {
        $('#mark-attendance-modal').removeClass('hidden').addClass('flex');
    });

    // Close modal
    $('#close-attendance-modal, #cancel-attendance').click(function() {
        $('#mark-attendance-modal').removeClass('flex').addClass('hidden');
        $('#students-attendance-list').addClass('hidden');
    });

    // Load students for attendance
    $('#load-students-btn').click(function() {
        const classId = $('#attendance-class').val();
        const sectionId = $('#attendance-section').val();

        if (!classId || !sectionId) {
            showToast('Please select class and section', 'error');
            return;
        }

        // Load students via AJAX
        $.post('<?= base_url($route_prefix . "/get-by-class-section") ?>', {
            class_id: classId,
            section_id: sectionId
        })
        .done(function(response) {
            if (response.success) {
                displayStudentsForAttendance(response.data);
                $('#students-attendance-list').removeClass('hidden');
            } else {
                showToast('Failed to load students', 'error');
            }
        });
    });

    // Mark attendance form submission
    $('#mark-attendance-form').submit(function(e) {
        e.preventDefault();
        
        const attendanceData = {};
        $('.student-attendance').each(function() {
            const studentSessionId = $(this).data('student-session-id');
            const attendanceType = $(this).val();
            if (attendanceType) {
                attendanceData[studentSessionId] = attendanceType;
            }
        });

        if (Object.keys(attendanceData).length === 0) {
            showToast('Please mark attendance for at least one student', 'error');
            return;
        }

        const formData = {
            attendance: attendanceData,
            date: $('input[name="date"]').val()
        };
        
        $.post('<?= base_url($route_prefix . "/mark-attendance") ?>', formData)
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    $('#mark-attendance-modal').removeClass('flex').addClass('hidden');
                    loadAttendanceData();
                } else {
                    showToast(response.message, 'error');
                }
            })
            .fail(function() {
                showToast('An error occurred while marking attendance', 'error');
            });
    });

    function loadAttendanceData() {
        const filters = {
            class_id: $('#filter-class').val(),
            section_id: $('#filter-section').val(),
            date_from: $('#filter-date-from').val(),
            date_to: $('#filter-date-to').val(),
            attendance_type_id: $('#filter-attendance-type').val()
        };

        $.post('<?= base_url($route_prefix . "/attendance-report") ?>', filters)
            .done(function(response) {
                if (response.success) {
                    displayAttendanceData(response.data);
                } else {
                    $('#attendance-tbody').html('<tr><td colspan="7" class="text-center py-4">No attendance records found</td></tr>');
                }
            })
            .fail(function() {
                $('#attendance-tbody').html('<tr><td colspan="7" class="text-center py-4">Error loading attendance data</td></tr>');
            });
    }

    function displayAttendanceData(data) {
        let html = '';
        if (data.length > 0) {
            data.forEach(function(record) {
                html += `
                    <tr>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.firstname} ${record.lastname || ''}
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.admission_no || 'N/A'}
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.class || 'N/A'}
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.section || 'N/A'}
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.date}
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <span class="inline-flex rounded-full bg-opacity-10 py-1 px-3 text-sm font-medium ${getAttendanceTypeClass(record.attendance_type)}">
                                ${record.attendance_type}
                            </span>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            ${record.remark || '-'}
                        </td>
                    </tr>
                `;
            });
        } else {
            html = '<tr><td colspan="7" class="text-center py-4">No attendance records found</td></tr>';
        }
        $('#attendance-tbody').html(html);
    }

    function displayStudentsForAttendance(students) {
        let html = '';
        students.forEach(function(student) {
            html += `
                <tr>
                    <td class="border-b border-[#eee] py-3 px-4 dark:border-strokedark">
                        ${student.firstname} ${student.lastname || ''}
                    </td>
                    <td class="border-b border-[#eee] py-3 px-4 dark:border-strokedark">
                        ${student.admission_no || 'N/A'}
                    </td>
                    <td class="border-b border-[#eee] py-3 px-4 dark:border-strokedark">
                        <select class="student-attendance w-full rounded border-[1.5px] border-stroke bg-transparent py-2 px-3 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" data-student-session-id="${student.id}">
                            <option value="">Select</option>
                            <?php foreach ($attendance_types as $id => $type): ?>
                                <option value="<?= $id ?>"><?= $type ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
            `;
        });
        $('#students-list').html(html);
    }

    function getAttendanceTypeClass(type) {
        switch(type.toLowerCase()) {
            case 'present':
                return 'bg-success text-success';
            case 'absent':
                return 'bg-danger text-danger';
            case 'late':
                return 'bg-warning text-warning';
            case 'half day':
                return 'bg-info text-info';
            default:
                return 'bg-gray text-gray';
        }
    }
});
</script>
<?= $this->endSection() ?>
