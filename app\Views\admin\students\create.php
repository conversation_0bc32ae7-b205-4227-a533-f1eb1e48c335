<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Student Form -->
<div class="grid grid-cols-1 gap-9 sm:grid-cols-1">
    <div class="flex flex-col gap-9">
        <!-- Student Information Form -->
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Student Information
                </h3>
            </div>
            <form id="student-form" enctype="multipart/form-data">
                <?php if (isset($record)): ?>
                    <input type="hidden" name="_method" value="PUT">
                    <input type="hidden" name="id" value="<?= $record['id'] ?>">
                <?php endif; ?>
                <div class="p-6.5">
                    <!-- Basic Information -->
                    <div class="mb-6">
                        <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Basic Information</h4>
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Admission Number <span class="text-meta-1">*</span>
                                </label>
                                <input type="text" name="admission_no" id="admission_no" placeholder="Auto-generated if empty" 
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    First Name <span class="text-meta-1">*</span>
                                </label>
                                <input type="text" name="firstname" id="firstname" placeholder="Enter first name" required
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Last Name <span class="text-meta-1">*</span>
                                </label>
                                <input type="text" name="lastname" id="lastname" placeholder="Enter last name" required
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Middle Name
                                </label>
                                <input type="text" name="middlename" id="middlename" placeholder="Enter middle name"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Roll Number
                                </label>
                                <input type="text" name="roll_no" id="roll_no" placeholder="Enter roll number"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Admission Date
                                </label>
                                <input type="date" name="admission_date" id="admission_date" value="<?= date('Y-m-d') ?>"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Date of Birth
                                </label>
                                <input type="date" name="dob" id="dob"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Gender
                                </label>
                                <select name="gender" id="gender"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                                    <option value="">Select Gender</option>
                                    <?php foreach ($genders as $value => $label): ?>
                                        <option value="<?= $value ?>"><?= $label ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Blood Group
                                </label>
                                <select name="blood_group" id="blood_group"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                                    <option value="">Select Blood Group</option>
                                    <?php foreach ($blood_groups as $value => $label): ?>
                                        <option value="<?= $value ?>"><?= $label ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="mb-6">
                        <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Contact Information</h4>
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Email
                                </label>
                                <input type="email" name="email" id="email" placeholder="Enter email address"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Mobile Number
                                </label>
                                <input type="tel" name="mobileno" id="mobileno" placeholder="Enter mobile number"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Current Address
                                </label>
                                <textarea name="current_address" id="current_address" rows="3" placeholder="Enter current address"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Permanent Address
                                </label>
                                <textarea name="permanent_address" id="permanent_address" rows="3" placeholder="Enter permanent address"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Upload -->
                    <div class="mb-6">
                        <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Student Photo</h4>
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Upload Photo
                                </label>
                                <input type="file" name="image" id="image" accept="image/*"
                                    class="w-full cursor-pointer rounded-lg border-[1.5px] border-stroke bg-transparent font-medium outline-none transition file:mr-5 file:border-collapse file:cursor-pointer file:border-0 file:border-r file:border-solid file:border-stroke file:bg-whiter file:py-3 file:px-5 file:hover:bg-primary file:hover:bg-opacity-10 focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:file:border-form-strokedark dark:file:bg-white/30 dark:file:text-white dark:focus:border-primary" />
                            </div>
                            <div id="image-preview" class="hidden">
                                <label class="mb-2.5 block text-black dark:text-white">Preview</label>
                                <img id="preview-img" src="" alt="Preview" class="h-24 w-24 rounded-lg object-cover">
                            </div>
                        </div>
                    </div>

                    <!-- Guardian Information -->
                    <div class="mb-6">
                        <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Guardian Information</h4>
                        <div class="mb-4">
                            <label class="mb-2.5 block text-black dark:text-white">
                                Guardian Is
                            </label>
                            <select name="guardian_is" id="guardian_is"
                                class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                                <option value="father">Father</option>
                                <option value="mother">Mother</option>
                                <option value="other">Other Guardian</option>
                            </select>
                        </div>

                        <!-- Father Information -->
                        <div id="father-info" class="mb-6">
                            <h5 class="mb-3 text-md font-medium text-black dark:text-white">Father Information</h5>
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Father Name</label>
                                    <input type="text" name="father_name" id="father_name" placeholder="Enter father name"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Father Phone</label>
                                    <input type="tel" name="father_phone" id="father_phone" placeholder="Enter father phone"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Father Occupation</label>
                                    <input type="text" name="father_occupation" id="father_occupation" placeholder="Enter father occupation"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                            </div>
                        </div>

                        <!-- Mother Information -->
                        <div id="mother-info" class="mb-6">
                            <h5 class="mb-3 text-md font-medium text-black dark:text-white">Mother Information</h5>
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Mother Name</label>
                                    <input type="text" name="mother_name" id="mother_name" placeholder="Enter mother name"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Mother Phone</label>
                                    <input type="tel" name="mother_phone" id="mother_phone" placeholder="Enter mother phone"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Mother Occupation</label>
                                    <input type="text" name="mother_occupation" id="mother_occupation" placeholder="Enter mother occupation"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                            </div>
                        </div>

                        <!-- Other Guardian Information -->
                        <div id="guardian-info" class="mb-6 hidden">
                            <h5 class="mb-3 text-md font-medium text-black dark:text-white">Guardian Information</h5>
                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Guardian Name</label>
                                    <input type="text" name="guardian_name" id="guardian_name" placeholder="Enter guardian name"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Guardian Relation</label>
                                    <select name="guardian_relation" id="guardian_relation"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                                        <option value="">Select Relation</option>
                                        <?php foreach ($guardian_relations as $value => $label): ?>
                                            <option value="<?= $value ?>"><?= $label ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Guardian Phone</label>
                                    <input type="tel" name="guardian_phone" id="guardian_phone" placeholder="Enter guardian phone"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                                <div>
                                    <label class="mb-2.5 block text-black dark:text-white">Guardian Occupation</label>
                                    <input type="text" name="guardian_occupation" id="guardian_occupation" placeholder="Enter guardian occupation"
                                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" />
                                </div>
                            </div>
                            <div class="mt-4">
                                <label class="mb-2.5 block text-black dark:text-white">Guardian Address</label>
                                <textarea name="guardian_address" id="guardian_address" rows="3" placeholder="Enter guardian address"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="mb-6">
                        <h4 class="mb-4 text-lg font-semibold text-black dark:text-white">Additional Information</h4>
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Previous School
                                </label>
                                <textarea name="previous_school" id="previous_school" rows="3" placeholder="Enter previous school details"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
                            </div>

                            <div>
                                <label class="mb-2.5 block text-black dark:text-white">
                                    Notes
                                </label>
                                <textarea name="note" id="note" rows="3" placeholder="Enter any additional notes"
                                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-6">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Status
                        </label>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="yes" checked
                                class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                            <label for="is_active" class="ml-2 text-black dark:text-white">Active</label>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex gap-4">
                        <button type="submit" id="submit-btn"
                            class="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90 md:w-auto md:px-10">
                            <span class="loading-spinner hidden mr-2">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                            Save Student
                        </button>
                        <a href="<?= base_url($route_prefix) ?>"
                            class="flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white md:w-auto md:px-10">
                            Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Image preview
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').removeClass('hidden');
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').addClass('hidden');
        }
    });

    // Guardian type change
    $('#guardian_is').change(function() {
        const guardianType = $(this).val();
        if (guardianType === 'other') {
            $('#guardian-info').removeClass('hidden');
        } else {
            $('#guardian-info').addClass('hidden');
        }
    });

    // Form submission
    $('#student-form').submit(function(e) {
        e.preventDefault();
        
        const submitBtn = $('#submit-btn');
        const spinner = submitBtn.find('.loading-spinner');
        
        // Show loading state
        submitBtn.prop('disabled', true);
        spinner.removeClass('hidden');
        
        // Prepare form data
        const formData = new FormData(this);
        
        // Handle checkbox
        if (!$('#is_active').is(':checked')) {
            formData.set('is_active', 'no');
        }
        
        $.ajax({
            url: '<?= base_url($route_prefix . "/store") ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    setTimeout(function() {
                        window.location.href = '<?= base_url($route_prefix) ?>';
                    }, 1500);
                } else {
                    showToast(response.message, 'error');
                    if (response.errors) {
                        displayFormErrors(response.errors);
                    }
                }
            },
            error: function(xhr) {
                showToast('An error occurred while saving the student', 'error');
                console.error(xhr.responseText);
            },
            complete: function() {
                // Hide loading state
                submitBtn.prop('disabled', false);
                spinner.addClass('hidden');
            }
        });
    });

    function displayFormErrors(errors) {
        // Clear previous errors
        $('.error-message').remove();
        $('.border-red-500').removeClass('border-red-500');
        
        // Display new errors
        for (const field in errors) {
            const input = $(`[name="${field}"]`);
            if (input.length) {
                input.addClass('border-red-500');
                input.after(`<p class="error-message text-red-500 text-sm mt-1">${errors[field]}</p>`);
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
