<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Student Info Card -->
<div class="mb-6 rounded-sm border border-stroke bg-white p-6 shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="flex items-center gap-4">
        <?php 
        $imageUrl = !empty($student['image']) 
            ? base_url('writable/uploads/students/' . $student['image'])
            : 'https://ui-avatars.com/api/?name=' . urlencode($student['firstname'] . ' ' . $student['lastname']) . '&background=3b82f6&color=fff&size=60';
        ?>
        <img src="<?= $imageUrl ?>" alt="Student Photo" class="h-15 w-15 rounded-full object-cover">
        <div>
            <h3 class="text-lg font-semibold text-black dark:text-white">
                <?= esc($student['firstname'] . ' ' . ($student['lastname'] ?? '')) ?>
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Admission No: <?= esc($student['admission_no'] ?? 'N/A') ?>
            </p>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="mb-6 flex flex-wrap gap-3">
    <button id="enroll-session-btn" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-plus mr-2"></i>
        Enroll in Session
    </button>
    <a href="<?= base_url($route_prefix . '/edit/' . $student['id']) ?>" 
        class="inline-flex items-center justify-center rounded-md bg-green-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-edit mr-2"></i>
        Edit Student
    </a>
    <a href="<?= base_url($route_prefix) ?>" 
        class="inline-flex items-center justify-center rounded-md bg-gray-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to List
    </a>
</div>

<!-- Sessions Table -->
<div class="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h4 class="text-xl font-semibold text-black dark:text-white">
            Academic Sessions
        </h4>
    </div>
    
    <div class="max-w-full overflow-x-auto">
        <table class="w-full table-auto">
            <thead>
                <tr class="bg-gray-2 text-left dark:bg-meta-4">
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Session</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Class</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Section</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Transport Fees</th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Fees Discount</th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                    <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($sessions)): ?>
                    <?php foreach ($sessions as $session): ?>
                    <tr>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <?= esc($session['session'] ?? 'N/A') ?>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <?= esc($session['class'] ?? 'N/A') ?>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <?= esc($session['section'] ?? 'N/A') ?>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            $<?= number_format($session['transport_fees'] ?? 0, 2) ?>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            $<?= number_format($session['fees_discount'] ?? 0, 2) ?>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <span class="inline-flex rounded-full <?= $session['is_active'] === 'yes' ? 'bg-success bg-opacity-10 text-success' : 'bg-danger bg-opacity-10 text-danger' ?> py-1 px-3 text-sm font-medium">
                                <?= $session['is_active'] === 'yes' ? 'Active' : 'Inactive' ?>
                            </span>
                        </td>
                        <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                            <div class="flex items-center space-x-3.5">
                                <button class="edit-session-btn hover:text-primary" data-id="<?= $session['id'] ?>" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="toggle-session-btn hover:text-warning" data-id="<?= $session['id'] ?>" data-status="<?= $session['is_active'] ?>" title="Toggle Status">
                                    <i class="fas fa-toggle-<?= $session['is_active'] === 'yes' ? 'on' : 'off' ?>"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="border-b border-[#eee] py-5 px-4 text-center dark:border-strokedark">
                            No sessions found for this student.
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Enroll Session Modal -->
<div id="enroll-session-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-md rounded-lg bg-white p-6 dark:bg-boxdark">
        <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-black dark:text-white">Enroll in Session</h3>
            <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="enroll-session-form">
            <input type="hidden" name="student_id" value="<?= $student['id'] ?>">
            
            <div class="mb-4">
                <label class="mb-2.5 block text-black dark:text-white">Session <span class="text-meta-1">*</span></label>
                <select name="session_id" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                    <option value="">Select Session</option>
                    <?php foreach ($available_sessions as $id => $name): ?>
                        <option value="<?= $id ?>"><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="mb-2.5 block text-black dark:text-white">Class <span class="text-meta-1">*</span></label>
                <select name="class_id" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                    <option value="">Select Class</option>
                    <?php foreach ($classes as $id => $name): ?>
                        <option value="<?= $id ?>"><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="mb-2.5 block text-black dark:text-white">Section <span class="text-meta-1">*</span></label>
                <select name="section_id" required class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                    <option value="">Select Section</option>
                    <?php foreach ($sections as $id => $name): ?>
                        <option value="<?= $id ?>"><?= $name ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="mb-2.5 block text-black dark:text-white">Transport Fees</label>
                <input type="number" name="transport_fees" step="0.01" min="0" value="0" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
            </div>
            
            <div class="mb-6">
                <label class="mb-2.5 block text-black dark:text-white">Fees Discount</label>
                <input type="number" name="fees_discount" step="0.01" min="0" value="0" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
            </div>
            
            <div class="flex gap-3">
                <button type="submit" class="flex-1 rounded bg-primary p-3 font-medium text-white hover:bg-opacity-90">
                    Enroll Student
                </button>
                <button type="button" id="cancel-enroll" class="flex-1 rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Show enroll session modal
    $('#enroll-session-btn').click(function() {
        $('#enroll-session-modal').removeClass('hidden').addClass('flex');
    });

    // Close modal
    $('#close-modal, #cancel-enroll').click(function() {
        $('#enroll-session-modal').removeClass('flex').addClass('hidden');
    });

    // Enroll session form submission
    $('#enroll-session-form').submit(function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        
        $.post('<?= base_url($route_prefix . "/enroll-session") ?>', formData)
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showToast(response.message, 'error');
                }
            })
            .fail(function() {
                showToast('An error occurred while enrolling student', 'error');
            });
    });

    // Toggle session status
    $(document).on('click', '.toggle-session-btn', function() {
        const sessionId = $(this).data('id');
        const currentStatus = $(this).data('status');
        
        Swal.fire({
            title: 'Are you sure?',
            text: `Do you want to ${currentStatus === 'yes' ? 'deactivate' : 'activate'} this session?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, proceed!'
        }).then((result) => {
            if (result.isConfirmed) {
                // Toggle session status logic here
                showToast('Session status updated successfully', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
