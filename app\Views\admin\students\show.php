<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Action Buttons -->
<div class="mb-6 flex flex-wrap gap-3">
    <a href="<?= base_url($route_prefix . '/edit/' . $record['id']) ?>" 
        class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-edit mr-2"></i>
        Edit Student
    </a>
    <a href="<?= base_url($route_prefix) ?>" 
        class="inline-flex items-center justify-center rounded-md bg-gray-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to List
    </a>
    <button id="print-btn" 
        class="inline-flex items-center justify-center rounded-md bg-green-600 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90">
        <i class="fas fa-print mr-2"></i>
        Print
    </button>
</div>

<!-- Student Details -->
<div class="grid grid-cols-1 gap-9 sm:grid-cols-1">
    <div class="flex flex-col gap-9">
        <!-- Basic Information -->
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Basic Information
                </h3>
            </div>
            <div class="p-6.5">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <!-- Student Photo -->
                    <div class="md:col-span-1 lg:col-span-1">
                        <div class="text-center">
                            <?php 
                            $imageUrl = !empty($record['image']) 
                                ? base_url('writable/uploads/students/' . $record['image'])
                                : 'https://ui-avatars.com/api/?name=' . urlencode($record['firstname'] . ' ' . $record['lastname']) . '&background=3b82f6&color=fff&size=150';
                            ?>
                            <img src="<?= $imageUrl ?>" alt="Student Photo" 
                                class="mx-auto h-32 w-32 rounded-full object-cover border-4 border-primary">
                            <h4 class="mt-3 text-lg font-semibold text-black dark:text-white">
                                <?= esc($record['firstname'] . ' ' . ($record['lastname'] ?? '')) ?>
                            </h4>
                            <p class="text-gray-600 dark:text-gray-400">
                                <?= esc($record['admission_no'] ?? 'N/A') ?>
                            </p>
                            <span class="inline-flex rounded-full <?= $record['is_active'] === 'yes' ? 'bg-success bg-opacity-10 text-success' : 'bg-danger bg-opacity-10 text-danger' ?> py-1 px-3 text-sm font-medium">
                                <?= $record['is_active'] === 'yes' ? 'Active' : 'Inactive' ?>
                            </span>
                        </div>
                    </div>

                    <!-- Student Details -->
                    <div class="md:col-span-1 lg:col-span-2">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Admission Number</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['admission_no'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Roll Number</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['roll_no'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">First Name</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['firstname'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Name</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['lastname'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date of Birth</label>
                                <p class="mt-1 text-black dark:text-white"><?= !empty($record['dob']) ? date('F j, Y', strtotime($record['dob'])) : 'N/A' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Gender</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['gender'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Blood Group</label>
                                <p class="mt-1 text-black dark:text-white"><?= esc($record['blood_group'] ?? 'N/A') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Admission Date</label>
                                <p class="mt-1 text-black dark:text-white"><?= !empty($record['admission_date']) ? date('F j, Y', strtotime($record['admission_date'])) : 'N/A' ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Contact Information
                </h3>
            </div>
            <div class="p-6.5">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['email'] ?? 'N/A') ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Mobile Number</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['mobileno'] ?? 'N/A') ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Address</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['current_address'] ?? 'N/A') ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Permanent Address</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['permanent_address'] ?? 'N/A') ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Guardian Information -->
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Guardian Information
                </h3>
            </div>
            <div class="p-6.5">
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Guardian Is</label>
                    <p class="mt-1 text-black dark:text-white"><?= ucfirst(esc($record['guardian_is'] ?? 'N/A')) ?></p>
                </div>

                <!-- Father Information -->
                <div class="mb-6">
                    <h4 class="mb-3 text-md font-medium text-black dark:text-white">Father Information</h4>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['father_name'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['father_phone'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Occupation</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['father_occupation'] ?? 'N/A') ?></p>
                        </div>
                    </div>
                </div>

                <!-- Mother Information -->
                <div class="mb-6">
                    <h4 class="mb-3 text-md font-medium text-black dark:text-white">Mother Information</h4>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['mother_name'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['mother_phone'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Occupation</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['mother_occupation'] ?? 'N/A') ?></p>
                        </div>
                    </div>
                </div>

                <!-- Guardian Information (if other) -->
                <?php if (!empty($record['guardian_name'])): ?>
                <div class="mb-6">
                    <h4 class="mb-3 text-md font-medium text-black dark:text-white">Guardian Information</h4>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['guardian_name']) ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Relation</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['guardian_relation'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['guardian_phone'] ?? 'N/A') ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Occupation</label>
                            <p class="mt-1 text-black dark:text-white"><?= esc($record['guardian_occupation'] ?? 'N/A') ?></p>
                        </div>
                    </div>
                    <?php if (!empty($record['guardian_address'])): ?>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['guardian_address']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Additional Information -->
        <?php if (!empty($record['previous_school']) || !empty($record['note'])): ?>
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Additional Information
                </h3>
            </div>
            <div class="p-6.5">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <?php if (!empty($record['previous_school'])): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Previous School</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['previous_school']) ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($record['note'])): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <p class="mt-1 text-black dark:text-white"><?= esc($record['note']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Academic Information -->
        <?php if (isset($record['session_data']) && !empty($record['session_data'])): ?>
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">
                    Academic Information
                </h3>
            </div>
            <div class="p-6.5">
                <div class="overflow-x-auto">
                    <table class="w-full table-auto">
                        <thead>
                            <tr class="bg-gray-2 text-left dark:bg-meta-4">
                                <th class="py-4 px-4 font-medium text-black dark:text-white">Session</th>
                                <th class="py-4 px-4 font-medium text-black dark:text-white">Class</th>
                                <th class="py-4 px-4 font-medium text-black dark:text-white">Section</th>
                                <th class="py-4 px-4 font-medium text-black dark:text-white">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($record['session_data'] as $session): ?>
                            <tr>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <?= esc($session['session'] ?? 'N/A') ?>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <?= esc($session['class'] ?? 'N/A') ?>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <?= esc($session['section'] ?? 'N/A') ?>
                                </td>
                                <td class="border-b border-[#eee] py-5 px-4 dark:border-strokedark">
                                    <span class="inline-flex rounded-full <?= $session['is_active'] === 'yes' ? 'bg-success bg-opacity-10 text-success' : 'bg-danger bg-opacity-10 text-danger' ?> py-1 px-3 text-sm font-medium">
                                        <?= $session['is_active'] === 'yes' ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Print functionality
    $('#print-btn').click(function() {
        window.print();
    });
});

// Print styles
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .dark\:bg-boxdark,
    .dark\:border-strokedark {
        background: white !important;
        border-color: #e5e7eb !important;
    }
    
    .dark\:text-white {
        color: black !important;
    }
}
</script>
<?= $this->endSection() ?>
