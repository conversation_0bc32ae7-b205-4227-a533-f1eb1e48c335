<?php
/**
 * Student Management System Setup Checker
 * This script checks if everything is properly configured
 */

echo "====================================\n";
echo "Student Management System Setup Check\n";
echo "====================================\n\n";

// Check PHP version
echo "1. PHP Version Check:\n";
$phpVersion = phpversion();
echo "   Current PHP Version: $phpVersion\n";
if (version_compare($phpVersion, '8.1.0', '>=')) {
    echo "   ✓ PHP version is compatible\n\n";
} else {
    echo "   ✗ PHP 8.1+ required\n\n";
}

// Check required extensions
echo "2. Required Extensions:\n";
$requiredExtensions = ['mysqli', 'mbstring', 'json', 'curl', 'openssl', 'fileinfo'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✓ $ext extension loaded\n";
    } else {
        echo "   ✗ $ext extension missing\n";
    }
}
echo "\n";

// Check .env file
echo "3. Environment Configuration:\n";
if (file_exists('.env')) {
    echo "   ✓ .env file exists\n";
    
    // Parse .env file
    $envContent = file_get_contents('.env');
    
    // Check database configuration
    if (strpos($envContent, 'database.default.database = studentwablas') !== false) {
        echo "   ✓ Database configured for 'studentwablas'\n";
    } else {
        echo "   ⚠ Database configuration may need review\n";
    }
    
    // Check base URL
    if (strpos($envContent, 'app.baseURL') !== false) {
        echo "   ✓ Base URL configured\n";
    } else {
        echo "   ⚠ Base URL not configured\n";
    }
    
    // Check encryption key
    if (strpos($envContent, 'encryption.key = hex2bin:') !== false) {
        echo "   ✓ Encryption key configured\n";
    } else {
        echo "   ⚠ Encryption key not configured\n";
    }
} else {
    echo "   ✗ .env file not found\n";
}
echo "\n";

// Check writable directories
echo "4. Directory Permissions:\n";
$writableDirs = ['writable/cache', 'writable/logs', 'writable/session', 'writable/uploads'];
foreach ($writableDirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "   ✓ $dir is writable\n";
    } else {
        echo "   ✗ $dir is not writable or doesn't exist\n";
    }
}
echo "\n";

// Check admin files
echo "5. Admin Dashboard Files:\n";
$adminFiles = [
    'app/Controllers/Admin.php',
    'app/Models/DashboardModel.php',
    'app/Views/admin/layout.php',
    'app/Views/admin/dashboard.php',
    'public/assets/admin/css/admin.css',
    'public/assets/admin/js/admin.js'
];

foreach ($adminFiles as $file) {
    if (file_exists($file)) {
        echo "   ✓ $file exists\n";
    } else {
        echo "   ✗ $file missing\n";
    }
}
echo "\n";

// Check database connection (if possible)
echo "6. Database Connection:\n";
try {
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        preg_match('/database\.default\.hostname = (.+)/', $envContent, $hostMatch);
        preg_match('/database\.default\.database = (.+)/', $envContent, $dbMatch);
        preg_match('/database\.default\.username = (.+)/', $envContent, $userMatch);
        preg_match('/database\.default\.password = (.*)/', $envContent, $passMatch);
        
        if ($hostMatch && $dbMatch && $userMatch) {
            $host = trim($hostMatch[1]);
            $database = trim($dbMatch[1]);
            $username = trim($userMatch[1]);
            $password = isset($passMatch[1]) ? trim($passMatch[1]) : '';
            
            if (extension_loaded('mysqli')) {
                $connection = @mysqli_connect($host, $username, $password, $database);
                if ($connection) {
                    echo "   ✓ Database connection successful\n";
                    mysqli_close($connection);
                } else {
                    echo "   ✗ Database connection failed: " . mysqli_connect_error() . "\n";
                }
            } else {
                echo "   ⚠ MySQLi extension not available for testing\n";
            }
        } else {
            echo "   ⚠ Database configuration incomplete\n";
        }
    } else {
        echo "   ⚠ Cannot test - .env file missing\n";
    }
} catch (Exception $e) {
    echo "   ✗ Database test error: " . $e->getMessage() . "\n";
}
echo "\n";

// Check hosts file configuration
echo "7. Domain Configuration:\n";
$hostOutput = shell_exec('ping studentwablas.me -n 1 2>&1');
if (strpos($hostOutput, '127.0.0.1') !== false) {
    echo "   ✓ studentwablas.me resolves to localhost\n";
} else {
    echo "   ⚠ studentwablas.me may not be configured in hosts file\n";
}
echo "\n";

echo "====================================\n";
echo "Setup Check Complete!\n";
echo "====================================\n\n";

echo "Next Steps:\n";
echo "1. If all checks pass, run: php spark serve --host studentwablas.me --port 80\n";
echo "2. Or use the provided start-server.bat / start-server.ps1 scripts\n";
echo "3. Access your site at: http://studentwablas.me/\n";
echo "4. Access admin dashboard at: http://studentwablas.me/admin\n\n";

echo "For port 80, you may need to run as administrator.\n";
echo "Alternative: Use port 8080 with --port 8080\n\n";
?>
