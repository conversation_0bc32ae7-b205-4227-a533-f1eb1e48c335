<?php
/**
 * Debug script to check dashboard data
 */

// Load CodeIgniter
require_once 'vendor/autoload.php';

// Bootstrap CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Get database connection
$db = \Config\Database::connect();

echo "====================================\n";
echo "Dashboard Data Debug\n";
echo "====================================\n\n";

// Check Students
echo "1. Students Data:\n";
try {
    $students = $db->table('students')->get()->getResultArray();
    echo "   Total Students: " . count($students) . "\n";
    if (!empty($students)) {
        foreach ($students as $student) {
            echo "   - {$student['firstname']} {$student['lastname']} (ID: {$student['id']})\n";
        }
    }
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Check Staff
echo "2. Staff Data:\n";
try {
    $staff = $db->table('staff')->get()->getResultArray();
    echo "   Total Staff: " . count($staff) . "\n";
    if (!empty($staff)) {
        foreach ($staff as $member) {
            echo "   - {$member['name']} ({$member['designation']})\n";
        }
    }
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Check Classes
echo "3. Classes Data:\n";
try {
    $classes = $db->table('classes')->get()->getResultArray();
    echo "   Total Classes: " . count($classes) . "\n";
    if (!empty($classes)) {
        foreach ($classes as $class) {
            echo "   - {$class['class']} (ID: {$class['id']})\n";
        }
    }
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Check Expenses
echo "4. Expenses Data:\n";
try {
    $expenses = $db->table('expenses')->get()->getResultArray();
    echo "   Total Expenses: " . count($expenses) . "\n";
    $totalAmount = 0;
    if (!empty($expenses)) {
        foreach ($expenses as $expense) {
            echo "   - {$expense['name']}: $" . number_format($expense['amount'], 2) . "\n";
            $totalAmount += $expense['amount'];
        }
        echo "   Total Amount: $" . number_format($totalAmount, 2) . "\n";
    }
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test Dashboard Model
echo "5. Dashboard Model Test:\n";
try {
    $dashboardModel = new \App\Models\DashboardModel();
    
    $stats = $dashboardModel->getDashboardStats();
    echo "   Dashboard Stats:\n";
    echo "   - Total Students: {$stats['total_students']}\n";
    echo "   - Total Staff: {$stats['total_staff']}\n";
    echo "   - Total Classes: {$stats['total_classes']}\n";
    echo "   - Monthly Expenses: $" . number_format($stats['monthly_expenses'], 2) . "\n";
    
    $activities = $dashboardModel->getRecentActivities();
    echo "   Recent Activities: " . count($activities) . " items\n";
    
    $charts = $dashboardModel->getChartsData();
    echo "   Charts Data:\n";
    echo "   - Enrollments: " . count($charts['enrollments']) . " data points\n";
    echo "   - Expenses: " . count($charts['expenses']) . " categories\n";
    
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}
echo "\n";

// Check table structure
echo "6. Table Structure Check:\n";
$tables = ['students', 'staff', 'classes', 'expenses', 'expense_head'];
foreach ($tables as $table) {
    try {
        if ($db->tableExists($table)) {
            $fields = $db->getFieldNames($table);
            echo "   ✓ $table exists with " . count($fields) . " columns\n";
        } else {
            echo "   ✗ $table does not exist\n";
        }
    } catch (Exception $e) {
        echo "   ⚠ $table check failed: " . $e->getMessage() . "\n";
    }
}
echo "\n";

echo "====================================\n";
echo "Debug Complete!\n";
echo "====================================\n";
echo "If you see data above, the dashboard should work properly.\n";
echo "Access your dashboard at: http://studentwablas.me/admin\n\n";
?>
