@echo off
echo ====================================
echo Student Management System Server
echo ====================================
echo.

REM Check if running as administrator for port 80
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
    echo Starting server on http://studentwablas.me:80
    php spark serve --host studentwablas.me --port 80
) else (
    echo.
    echo WARNING: Not running as administrator!
    echo Port 80 requires administrator privileges.
    echo.
    echo Choose an option:
    echo 1. Start on port 8080 (recommended)
    echo 2. Start on port 80 (requires admin restart)
    echo 3. Exit
    echo.
    set /p choice="Enter your choice (1-3): "
    
    if "%choice%"=="1" (
        echo.
        echo Starting server on http://studentwablas.me:8080
        echo You can access the site at: http://studentwablas.me:8080
        echo Admin dashboard at: http://studentwablas.me:8080/admin
        echo.
        php spark serve --host studentwablas.me --port 8080
    ) else if "%choice%"=="2" (
        echo.
        echo Please restart this script as administrator to use port 80.
        echo Right-click on start-server.bat and select "Run as administrator"
        pause
    ) else (
        echo Exiting...
        exit /b
    )
)

echo.
echo Server stopped.
pause
