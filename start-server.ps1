# Student Management System Server Startup Script
# PowerShell version

Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Student Management System Server" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Host "Running with administrator privileges..." -ForegroundColor Green
    Write-Host "Starting server on http://studentwablas.me:80" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Access your application at:" -ForegroundColor White
    Write-Host "  Main Site: http://studentwablas.me/" -ForegroundColor Cyan
    Write-Host "  Admin Dashboard: http://studentwablas.me/admin" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
    Write-Host ""
    
    # Start the server
    php spark serve --host studentwablas.me --port 80
} else {
    Write-Host "WARNING: Not running as administrator!" -ForegroundColor Red
    Write-Host "Port 80 requires administrator privileges." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Choose an option:" -ForegroundColor White
    Write-Host "1. Start on port 8080 (recommended)" -ForegroundColor Green
    Write-Host "2. Start on port 80 (requires admin restart)" -ForegroundColor Yellow
    Write-Host "3. Exit" -ForegroundColor Red
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-3)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "Starting server on http://studentwablas.me:8080" -ForegroundColor Green
            Write-Host ""
            Write-Host "Access your application at:" -ForegroundColor White
            Write-Host "  Main Site: http://studentwablas.me:8080/" -ForegroundColor Cyan
            Write-Host "  Admin Dashboard: http://studentwablas.me:8080/admin" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
            Write-Host ""
            
            # Start the server on port 8080
            php spark serve --host studentwablas.me --port 8080
        }
        "2" {
            Write-Host ""
            Write-Host "Please restart PowerShell as administrator to use port 80." -ForegroundColor Yellow
            Write-Host "Right-click on PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
            Write-Host "Then run this script again." -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
        }
        default {
            Write-Host "Exiting..." -ForegroundColor Gray
            exit
        }
    }
}

Write-Host ""
Write-Host "Server stopped." -ForegroundColor Red
Read-Host "Press Enter to exit"
