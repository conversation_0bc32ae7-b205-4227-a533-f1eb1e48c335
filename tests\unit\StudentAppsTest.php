<?php

namespace Tests\Unit;

use CodeIgniter\Test\CIUnitTestCase;
use CodeIgniter\Test\DatabaseTestTrait;
use App\Controllers\StudentAppsController;
use App\Controllers\StudentApplyLeaveController;
use App\Controllers\StudentAttendanceController;
use App\Controllers\StudentDocController;
use App\Models\StudentsModel;
use App\Models\StudentSessionModel;
use App\Models\StudentApplyLeaveModel;
use App\Models\SectionsModel;

class StudentAppsTest extends CIUnitTestCase
{
    use DatabaseTestTrait;

    protected $migrate     = true;
    protected $migrateOnce = false;
    protected $refresh     = true;
    protected $namespace   = null;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure database tables exist
        $this->createTestTables();
        $this->seedTestData();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }

    /**
     * Create test tables if they don't exist
     */
    private function createTestTables()
    {
        $forge = \Config\Database::forge();

        // Create sections table if not exists
        if (!$this->db->tableExists('sections')) {
            $forge->addField([
                'id' => [
                    'type'           => 'INT',
                    'constraint'     => 11,
                    'unsigned'       => true,
                    'auto_increment' => true,
                ],
                'section' => [
                    'type'       => 'VARCHAR',
                    'constraint' => 60,
                ],
                'is_active' => [
                    'type'       => 'VARCHAR',
                    'constraint' => 255,
                    'default'    => 'no',
                ],
                'created_at' => [
                    'type' => 'TIMESTAMP',
                    'null' => false,
                    'default' => 'CURRENT_TIMESTAMP',
                ],
            ]);
            $forge->addKey('id', true);
            $forge->createTable('sections');
        }

        // Create classes table if not exists
        if (!$this->db->tableExists('classes')) {
            $forge->addField([
                'id' => [
                    'type'           => 'INT',
                    'constraint'     => 11,
                    'unsigned'       => true,
                    'auto_increment' => true,
                ],
                'class' => [
                    'type'       => 'VARCHAR',
                    'constraint' => 60,
                ],
                'is_active' => [
                    'type'       => 'VARCHAR',
                    'constraint' => 255,
                    'default'    => 'no',
                ],
                'created_at' => [
                    'type' => 'TIMESTAMP',
                    'null' => false,
                    'default' => 'CURRENT_TIMESTAMP',
                ],
            ]);
            $forge->addKey('id', true);
            $forge->createTable('classes');
        }
    }

    /**
     * Seed test data
     */
    private function seedTestData()
    {
        // Insert test sections
        $this->db->table('sections')->insertBatch([
            ['section' => 'A', 'is_active' => 'yes'],
            ['section' => 'B', 'is_active' => 'yes'],
        ]);

        // Insert test classes
        $this->db->table('classes')->insertBatch([
            ['class' => '1st', 'is_active' => 'yes'],
            ['class' => '2nd', 'is_active' => 'yes'],
        ]);
    }

    /**
     * Test Student Apps Controller Index
     */
    public function testStudentAppsControllerIndex()
    {
        $controller = new StudentAppsController();
        $result = $controller->index();
        
        $this->assertInstanceOf('CodeIgniter\HTTP\Response', $result);
    }

    /**
     * Test Student Apps Statistics
     */
    public function testStudentAppsGetStats()
    {
        $controller = new StudentAppsController();
        
        // Mock request as AJAX
        $request = service('request');
        $request->setHeader('X-Requested-With', 'XMLHttpRequest');
        
        $result = $controller->getStats();
        $this->assertInstanceOf('CodeIgniter\HTTP\Response', $result);
    }

    /**
     * Test Sections Model
     */
    public function testSectionsModel()
    {
        $sectionsModel = new SectionsModel();
        
        // Test getting all sections
        $sections = $sectionsModel->findAll();
        $this->assertIsArray($sections);
        $this->assertGreaterThan(0, count($sections));
        
        // Test getting for dropdown
        $dropdown = $sectionsModel->getForDropdown();
        $this->assertIsArray($dropdown);
    }

    /**
     * Test Student Apply Leave Controller
     */
    public function testStudentApplyLeaveController()
    {
        $controller = new StudentApplyLeaveController();
        $result = $controller->index();
        
        $this->assertInstanceOf('CodeIgniter\HTTP\Response', $result);
    }

    /**
     * Test Student Attendance Controller
     */
    public function testStudentAttendanceController()
    {
        $controller = new StudentAttendanceController();
        $result = $controller->index();
        
        $this->assertInstanceOf('CodeIgniter\HTTP\Response', $result);
    }

    /**
     * Test Database Tables Exist
     */
    public function testRequiredTablesExist()
    {
        $requiredTables = [
            'sections',
            'classes',
            'students',
            'student_session',
            'student_applyleave',
            'student_attendences',
            'student_behaviour',
            'student_doc',
            'student_subject_attendances',
        ];

        foreach ($requiredTables as $table) {
            if ($this->db->tableExists($table)) {
                $this->assertTrue(true, "Table {$table} exists");
            } else {
                $this->markTestSkipped("Table {$table} does not exist in database");
            }
        }
    }

    /**
     * Test Routes Configuration
     */
    public function testStudentAppsRoutes()
    {
        $routes = service('routes');
        
        // Test main student apps route
        $this->assertTrue(true, 'Routes test placeholder');
        
        // Note: Actual route testing would require more complex setup
        // This is a placeholder for route validation
    }

    /**
     * Test View Files Exist
     */
    public function testViewFilesExist()
    {
        $requiredViews = [
            'admin/student_apps/index.php',
            'admin/student_apps/apply_leave/index.php',
            'admin/student_apps/attendance/index.php',
            'admin/student_apps/documents/index.php',
            'admin/student_apps/documents/create.php',
            'admin/student_apps/subject_attendance/index.php',
        ];

        foreach ($requiredViews as $view) {
            $viewPath = APPPATH . 'Views/' . $view;
            $this->assertFileExists($viewPath, "View file {$view} should exist");
        }
    }

    /**
     * Test Controller Files Exist
     */
    public function testControllerFilesExist()
    {
        $requiredControllers = [
            'StudentAppsController.php',
            'StudentApplyLeaveController.php',
            'StudentAttendanceController.php',
            'StudentSubjectAttendanceController.php',
        ];

        foreach ($requiredControllers as $controller) {
            $controllerPath = APPPATH . 'Controllers/' . $controller;
            $this->assertFileExists($controllerPath, "Controller file {$controller} should exist");
        }
    }

    /**
     * Test Model Files Exist
     */
    public function testModelFilesExist()
    {
        $requiredModels = [
            'StudentsModel.php',
            'StudentSessionModel.php',
            'StudentApplyLeaveModel.php',
            'SectionsModel.php',
            'StudentSubjectAttendanceModel.php',
        ];

        foreach ($requiredModels as $model) {
            $modelPath = APPPATH . 'Models/' . $model;
            $this->assertFileExists($modelPath, "Model file {$model} should exist");
        }
    }
}
